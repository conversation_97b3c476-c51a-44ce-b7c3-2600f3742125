import { HttpParams } from '@angular/common/http';
import { ErrorActionCode, IntegrationSource } from 'src/app/app.enum';
import { ModuleSettings } from 'src/app/core/interfaces/global-settings';

const paramsObj: any = new HttpParams();
paramsObj[Symbol.for('nonBlocking')] = true;
export const NO_BLOCK_PARAM = {
  params: paramsObj,
};

export const APP_VERSION = '1.0.656';

export const SUPP0RT_NO = '+91 80503 85050';

export const MAPS_KEY = 'AIzaSyCkkbA74XK_YdsvpDn02-jFwhomKKFp2Rc';

export const APP_STORE_URL =
  'https://apps.apple.com/in/app/leadrat-lite/id6547861817';
// 'https://apps.apple.com/in/app/leadrat-black/id6443775513';

export const PLAY_STORE_URL =
  'https://play.google.com/store/apps/details?id=com.leadrat.black.mobile.droid.leadrat&hl=en_IN&pli=1';
// 'https://play.google.com/store/apps/details?id=com.leadrat.black.mobile.droid';

export const ANDROID_APP_DOWNLOAD_LINK =
  'https://play.google.com/store/apps/details?id=com.leadrat.mobile.crm.droid';

// export const PRIVACY_POLICY_URL = 'https://www.leadrat.com/privacy-policy/';

// export const TERMS_URL = 'https://www.leadrat.com/terms/';

export const EMPTY_GUID = '00000000-0000-0000-0000-000000000000';

export const GOOGLE_BUTTON = 'assets/images/integration/google-logo.svg';

export const COMMUNICATION = [
  {
    name: 'Templates',
    image: 'assets/images/template.svg',
    description: 'Add / Edit Templates',
  },
  // {
  //   name: 'SMS',
  //   image: 'assets/images/sms.svg',
  //   description: 'setup & manage your SMS.',
  // },
  // {
  //   name: 'Email',
  //   image: 'assets/images/message.svg',
  //   description: 'setup & manage your email templates.',
  // },
];

export const INTEGRATION_LIST = [
  {
    name: IntegrationSource[5],
    displayName: 'Magic bricks',
    image: 'assets/images/integration/magicbricks.svg',
    logo: 'assets/images/integration/magicbricks-logo.svg',
    description:
      'platform for property buyers & sellers to locate properties of interest in India.',
    category: 1,
    value: 5
  },
  {
    name: IntegrationSource[7],
    displayName: IntegrationSource[7],
    image: 'assets/images/integration/housing.svg',
    logo: 'assets/images/integration/housing-logo.svg',
    description:
      'Housing.com is a Mumbai-based real estate search portal which allows customers to search for housing.',
    category: 1,
    value: 7
  },
  {
    name: IntegrationSource[6],
    displayName: '99 acres',
    image: 'assets/images/integration/99acres.svg',
    logo: 'assets/images/integration/99acres-logo.svg',
    description:
      'Platform for property buyers & sellers to locate properties of interest and source information about all property related issues.',
    category: 1,
    value: 6
  },
  // {
  //   name: IntegrationSource[18],
  //   displayName: 'Square yards',
  //   image: 'assets/images/integration/square-yards.svg',
  //   logo: 'assets/images/integration/square-yards-logo.svg',
  //   description:
  //     "Square Yards is India's largest integrated platform for Real Estate & Mortgages and one of the fastest growing Proptech platform.",
  // },
  {
    name: IntegrationSource[19],
    displayName: 'Quikr Homes',
    image: 'assets/images/integration/quikr-homes.svg',
    logo: 'assets/images/integration/quikr-homes-logo.svg',
    description:
      'Quikr Homes is a real estate platform that helps people search, buy, sell and rent properties in India.',
    category: 1,
    value: 19
  },
  {
    name: IntegrationSource[11],
    displayName: IntegrationSource[11],
    image: 'assets/images/integration/website.svg',
    logo: 'assets/images/integration/website-logo.svg',
    description:
      'A Website integration is when your website sends or receives information from another application, system.',
    category: 3,
    value: 11
  },
  {
    name: IntegrationSource[26],
    displayName: 'Estate Dekho',
    image: 'assets/images/integration/estateDekho.svg',
    logo: 'assets/images/integration/estateDekho-logo.svg',
    description:
      'Estate Dekho is a customer-driven, technology-focused real estate platform, challenging industry norms and setting new standards and To Provide One Stop Solution for end-to-end Business management and achieve True Cross Vertical Integration',
    category: 1,
    value: 26
  },
  // {
  //   name: IntegrationSource[25],
  //   displayName: 'OLX',
  //   image: 'assets/images/integration/olx.svg',
  //   logo: 'assets/images/integration/olx.svg',
  //   description:
  //     'Serving hundreds of millions of people every month, we help people buy and find housing, buy and sell household goods, and much more.',
  // },
  {
    name: IntegrationSource[32],
    displayName: 'Roofandfloor',
    image: 'assets/images/integration/roof-floor.svg',
    logo: 'assets/images/integration/roof-floor-logo.svg',
    category: 1,
    description:
      "RoofandFloor is India's fastest growing and most trusted home-buying platform. Launched by The Hindu Group in September 2014, it has quickly grown to influence over 6 Million homebuyers across the country, facilitating over 2 Million connections between builders and high-intent customers.",
    value: 32
  },
  {
    name: IntegrationSource[29],
    displayName: 'Real Estate India',
    category: 1,
    image: 'assets/images/integration/realEstateIndia.svg',
    logo: 'assets/images/integration/realEstateIndia-logo.svg',
    description:
      "RealestateIndia.Com is owned and managed by Weblink.In Pvt. Ltd, a leading brand in web designing services and e-commerce solutions. Weblink.In Pvt. Ltd. is counted for its expertise in web solutions and its top ranking business portals. Our invincible expertise and rich experience has raised our spirit to reach ahead from our client's expectation. Commendable success rate of other portals managed by Weblink is a live paradigm of our work excellence.",
    value: 29
  },
  {
    name: IntegrationSource[34],
    displayName: 'Property Wala',
    category: 1,
    image: 'assets/images/integration/property-wala.svg',
    logo: 'assets/images/integration/property-wala-logo.svg',
    description:
      'PropertyWala.com is a Real Estate-web based portal created, tested and maintained by a dedicated team of software professionals. The service is designed to cater for latest fast growing needs of owners/customers and dealers/promoters/builders in India.',
    value: 34
  },
  {
    name: IntegrationSource[36],
    displayName: 'MyGate',
    category: 1,
    image: 'assets/images/integration/mygate.svg',
    logo: 'assets/images/integration/mygate-logo.svg',
    description:
      'MyGate is a comprehensive security and community management platform designed to enhance the living experience in residential societies, gated communities, and apartment complexes.',
    value: 36
  },
  {
    name: IntegrationSource[37],
    displayName: 'Flipkart',
    category: 1,
    image: 'assets/images/integration/flipkart.svg',
    logo: 'assets/images/integration/flipkart-logo.svg',
    description:
      'Flipkart is a comprehensive security and community management platform designed to enhance the living experience in residential societies, gated communities, and apartment complexes.',
    value: 37,
  },
  {
    name: IntegrationSource[4],
    displayName: 'Google ads landing page',
    category: 2,
    image: 'assets/images/integration/google-ads-landing.svg',
    logo: 'assets/images/integration/ads.svg',
    description:
      'For each ad, you specify a final URL to determine the landing page where people are taken when they click your ad.',
    value: 4,
  },
  {
    name: IntegrationSource[4],
    displayName: 'Google ads leads form',
    category: 2,
    image: 'assets/images/integration/google-ads-form.svg',
    logo: 'assets/images/integration/ads.svg',
    description:
      'Lead forms help you generate leads by letting people submit their information in a form directly in your ad.',
    value: 4,
  },
  {
    name: IntegrationSource[33],
    displayName: 'Microsoft Ads',
    category: 2,
    image: 'assets/images/integration/microsoft.svg',
    logo: 'assets/images/integration/microsoft-logo.svg',
    description:
      'For each ad, you specify a final URL to determine the landing page where people are taken when they click your ad.',
    value: 33,
  },
  {
    name: IntegrationSource[12],
    displayName: IntegrationSource[12],
    image: 'assets/images/integration/gmail.svg',
    logo: 'assets/images/integration/gmail-logo.svg',
    category: 2,
    description:
      'Gmail has always had strong security as a foundation. We work hard to protect you from spam, phishing, and malware.',
    value: 12,
  },
  {
    name: IntegrationSource[21],
    displayName: 'Whatsapp',
    category: 1,
    image: 'assets/images/integration/whatsapp.svg',
    logo: 'assets/images/integration/whatsapp-logo.svg',
    description:
      'WhatsApp is a popular messaging application that allows users to send text messages, voice messages, make voice and video calls, and share images, documents, user locations, and other media. It is widely used for personal communication and business purposes, offering end-to-end encryption to ensure privacy and security.',
    value: 21,
  },
  {
    name: IntegrationSource[41],
    displayName: 'Webhook',
    category: 1,
    image: 'assets/images/integration/webhook.svg',
    logo: 'assets/images/integration/webhook-logo.svg',
    description:
      'Webhook is an online friend of property seekers who are looking for property to buy sale or rent.',
    value: 41,
  },
  {
    name: IntegrationSource[30],
    displayName: 'Common Floor',
    category: 1,
    image: 'assets/images/integration/commonFloor.svg',
    logo: 'assets/images/integration/commonFloor-logo.svg',
    description:
      'Commonfloor.com offers home seekers, sellers and real estate professionals an extensive online real estate ecosystem. This ecosystem innovatively combines at a single stop. Apart from a robust search mechanism Commonfloor also maintains a vast repository of research on a host of property related matters ranging from legal issues to maintenance to facility management to home insurance among many others.With over 70 lakh monthly online visits Commonfloor.com aims at adding value to each and every property pursuit.',
    value: 30,
  },
  {
    name: IntegrationSource[20],
    displayName: 'Just Lead',
    category: 1,
    image: 'assets/images/integration/just-lead.svg',
    logo: 'assets/images/integration/just-lead-logo.svg',
    description:
      'Justlead.in is an online friend of property seekers who are looking for property to buy sale or rent.',
    value: 20,
  },
  {
    name: IntegrationSource[39],
    displayName: 'Bayut',
    category: 1,
    image: 'assets/images/integration/bayut.svg',
    logo: 'assets/images/integration/bayut-logo.svg',
    description: '',
    value: 39,
  },
  {
    name: IntegrationSource[38],
    displayName: 'Property Finder',
    category: 1,
    image: 'assets/images/integration/property-finder.svg',
    logo: 'assets/images/integration/property-finder-logo.svg',
    description: '',
    value: 38,
  },
  {
    name: IntegrationSource[40],
    displayName: 'Dubizzle',
    category: 1,
    image: 'assets/images/integration/dubizzle.svg',
    logo: 'assets/images/integration/dubizzle-logo.svg',
    description: '',
    value: 40,
  },
  {
    name: IntegrationSource[2],
    displayName: IntegrationSource[2],
    image: 'assets/images/integration/facebook.svg',
    logo: 'assets/images/integration/facebook-logo.svg',
    description:
      'Facebook Integration in mobile app allows you to pass the app event data from the mobile app to Facebook.',
    category: 2,
    value: 2,
  },
  {
    name: 'GoogleCampaign',
    displayName: 'Google Campaign',
    image: 'assets/images/integration/google-ads.svg',
    logo: 'assets/images/integration/ads.svg',
    description:
      'Google Campaign Integration allows you to manage and track your Google Ads campaigns and leads.',
    category: 2,
    value: 44,
  },
  {
    name: IntegrationSource[1],
    displayName: IntegrationSource[1],
    image: 'assets/images/integration/ivr-logo.svg',
    logo: 'assets/images/integration/ivr-logo.svg',
    description: 'IVR Caller System Setup',
    category: 1,
    // value: 1,
  },
];

export const PAGE_SIZE: number = 10;
export const SHOW_ENTRIES: Array<number> = [10, 25, 50, 100, 200, 500];
export const MATCHING_RADIUS_LIST: any[] = [
  { label: '2 Kms', value: 2 },
  { label: '4 Kms', value: 4 },
  { label: '6 Kms', value: 6 },
  { label: '8 Kms', value: 8 },
  { label: '10 Kms', value: 10 },
  { label: '12 Kms', value: 12 },
  { label: '14 Kms', value: 14 },
  { label: '16 Kms', value: 16 },
  { label: '18 Kms', value: 18 },
  { label: '20 Kms', value: 20 },
];
export const LEAD_STATUS = {
  active: 'Active Leads',
  new: 'New',
  pending: 'Pending',
  scheduled: 'Scheduled',
  overdue: 'Overdue',
  expressionOfInterestLeadCount: 'Expression Of Interest',
  booked: 'Booked',
  bookingCancel: 'Booking Cancel',
  'not-interested': 'Not Interested',
  dropped: 'Dropped',
  all: 'All Leads',
  escalation: 'Escalated',
  highlighted: 'highlighted',
  aboutToconvert: 'about-to-convert',
  hot: 'hot',
  callback: 'callback',
  meetings: 'meetings',
  'site-visits': 'site-visits',
  'new-url': 'new',
};

export const LEAD_VISIBILITY_IMAGE = [
  {
    name: 'All',
    image: 'logos/muso-team.svg',
    count: 'allLeadsCount',
  },
  {
    name: 'My Leads',
    image: 'logos/single-muso.svg',
    count: 'myLeadsCount',
  },
  {
    name: "Team's",
    image: 'logos/muso-team.svg',
    count: 'teamLeadsCount',
  },
  {
    name: 'Unassigned',
    image: 'logos/unknown-person.svg',
    count: 'unassignLeadsCount',
  },
  {
    name: 'Deleted',
    image: 'logos/delete.svg',
    count: 'deletedLeadsCount',
  },
  {
    name: 'Duplicate',
    image: 'logos/multiple-files.svg',
    count: 'duplicateLeadsCount',
  },
];

export const USER_VISIBILITY = [
  {
    name: 'All',
    image: 'logos/muso-team.svg',
    userStatus: null,
  },
  {
    name: 'Active',
    image: 'logos/user-activate.svg',
    userStatus: 1,
  },
  {
    name: 'Inactive',
    image: 'logos/user-deactivate.svg',
    userStatus: 2,
  },
];

export const USER_VISIBILITY_IMAGE = [
  {
    name: 'All',
    image: 'logos/muso-team.svg',
    visibility: 'allUserCount',
    userStatus: null,
  },
  {
    name: 'Active',
    image: 'logos/user-activate.svg',
    visibility: 'activeUserCount',
    userStatus: true,
  },
  {
    name: 'Inactive',
    image: 'logos/user-deactivate.svg',
    visibility: 'inactiveUserCount',
    userStatus: false,
  },
];

export const LEAD_VISIBILITY = {
  selfWithReportee: 'All',
  self: 'My Leads',
  reportee: "Team's",
  unassignlead: 'Unassigned',
  deletedlead: 'Deleted',
  duplicate: 'Duplicate',
};

export const DATE_TYPE = [
  'All',
  'Created Date',
  'Scheduled Date',
  'Modified Date',
  'Deleted Date',
  'Possession Date',
  'Picked Date',
  'Booked Date',
  'Assigned Date',
];

export const REPORTS_DATE_TYPE = [
  'All',
  'Created Date',
  'Scheduled Date',
  'Modified Date',
  'Booked Date',
];

export const OWNER_TYPE = [
  'Both',
  'Primary Owner',
  'Secondary Owner',
]


export const PROP_DATE_TYPE = [
  'All',
  'Created Date',
  'Modified Date',
  'Possession Date',
];

export const LEAD_GENERATING_FROM = [
  'All',
  'Manual',
  'Duplicate',
  'Integration',
  'BulkUpload',
];

export const UPDATE_STATUS = {
  callback: 'Callback',
  'schedule-meeting': 'Schedule Meeting',
  'schedule-site-visit': 'Schedule Site Visit',
  book: 'Book',
  'not-interested': 'Not Interested',
  drop: 'Drop',
  'booking-cancel': 'Booking Cancel',
  invoice: 'Invoiced',
};
export const PROPERTY_BUDGET = [
  '<5 Lakhs',
  '5 Lakhs - 10 Lakhs',
  '10 Lakhs - 20 Lakhs',
  '20 Lakhs - 30 Lakhs',
  '30 Lakhs - 40 Lakhs',
  '40 Lakhs - 50 Lakhs',
  '>50 Lakhs',
];
export const PROPERTY_BUDGET_FILTER = [
  'Upto 10 lakhs',
  '10 Lakhs - 20 Lakhs',
  '20 Lakhs - 30 Lakhs',
  '30 Lakhs - 40 Lakhs',
  '40 Lakhs - 50 Lakhs',
  '50 lakhs to 1 Crore',
  'More than 1 Crore',
];
export const PROPERTY_BUDGET_VALUES = {
  [PROPERTY_BUDGET[0]]: [500000, 1000000],
  [PROPERTY_BUDGET[1]]: [1000000, 2000000],
  [PROPERTY_BUDGET[2]]: [2000000, 3000000],
  [PROPERTY_BUDGET[3]]: [3000000, 4000000],
  [PROPERTY_BUDGET[4]]: [4000000, 5000000],
  [PROPERTY_BUDGET[5]]: [5000000],
};
export const PROPERTY_TYPES = [
  { name: 'Residential', icon: 'ic-home-secondary' },
  { name: 'Commercial', icon: 'ic-building-secondary' },
  { name: 'Agricultural', icon: 'ic-leaf' },
];

export const PROPERTY_LIST = ['Residential', 'Commercial', 'Agricultural'];

export const WHATSAPP_SHARE_API = 'https://api.whatsapp.com/send';

export const LEAD_STATUS_REASONS = {
  [UPDATE_STATUS['callback']]: {
    'need-more-info': 'Need More Info',
    busy: 'Busy',
    'plan-postponed': 'Plan Postponed',
    'to-schedule-meeting': 'To schedule a meeting',
    'to-schedule-site-visit': 'To schedule a site visit',
    'not-answered': 'Not answered',
    'not-reachable': 'Not reachable',
  },
  [UPDATE_STATUS['not-interested']]: {
    'wrong-invalid-number': 'Wrong/invalid Number.',
    'different-budget': 'Unmatched Budget',
    'purchased-from-others': 'Purchased From Others',
    'different-location': 'Different Location',
    'different-requirement': 'Different Requirements',
  },
};
export const UPDATE_STATUS_PAST_TENSE = {
  'meeting-scheduled': 'Meeting Scheduled',
  'meeting-done': 'Meeting Done',
  'visit-done': 'Site Visit Done',
  'visit-scheduled': 'Site Visit Scheduled',
  'referral-scheduled': 'Referral Scheduled',
  booked: 'Booked',
  dropped: 'Dropped',
  'meeting-not-done': 'Meeting Not Done',
  'visit-not-done': 'Site Visit Not Done',
};
export const VALIDATION_CLEAR = 'CLEAR';

export const VALIDATION_SET = 'SET';
export const BHK = ['One', 'Two', 'Three', 'Four', 'More'];
export const LEAD_HISTORY_CATEGORY = [
  { dispName: 'All', value: 'All' },
  { dispName: 'Status', value: 'LeadStatus' },
  { dispName: 'Assignment', value: 'Assignment' },
  { dispName: 'Notes', value: 'Notes' },
];

export const HEADER_LIST = {
  tasks: 'Manage Tasks',
  'manage-properties': 'Manage Properties',
  'manage-projects': 'Manage Projects',
  leads: 'Leads',
  'role-permissions': 'Roles and Permissions',
  'manage-users': 'Manage Users',
  attendance: 'Attendance',
  'manage-team': 'Manage Team',
  'my-data': 'My Data',
  reports: 'Reports',
  'data-management': 'Data',
  facebook: 'Facebook',
  'manage-member': 'Manage Members',
  Teams: 'Teams',
  'manage-listing': 'Listing',
};

export const ALLOWED_EXCEL_FILE_TYPES =
  '.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel';
export const LEAD_EXCEL_TEMPLATE = 'assets/template/LeadTemplate.xlsx';
export const LEAD_ADV_EXCEL_TEMPLATE =
  'assets/template/LeadTemplate_Adv.xlsx';
export const USER_EXCEL_TEMPLATE = 'assets/template/UserTemplate.xlsx';
export const LOCATION_EXCEL_TEMPLATE = 'assets/template/LocationTemplate.xlsx';
export const PROPERTY_EXCEL_TEMPLATE = 'assets/template/PropertyTemplate.xlsx';
export const PROJECT_EXCEL_TEMPLATE = 'assets/template/ProjectTemplate.xlsx';
export const DATA_EXCEL_TEMPLATE = 'assets/template/DataTemplate.xlsx';
export const REFERENCE_EXCEL_TEMPLATE = 'assets/template/ReferenceIdTemplate.xlsx';
export const PROJECT_UNIT_EXCEL_TEMPLATE =
  'assets/template/ProjectUnitTemplate.xlsx';
export const DATA_MIGRATE_EXCEL_TEMPLATE =
  'assets/template/DataMigrateTemplate.xlsx';
export const LEAD_MIGRATE_EXCEL_TEMPLATE =
  'assets/template/LeadMigrateTemplate.xlsx';
export const LEAD_MIGRATE_ADV_EXCEL_TEMPLATE =
  'assets/template/LeadMigrateTemplate_Adv.xlsx';
export const LEAD_MIGRATE_REFFERAL_EXCEL_TEMPLATE =
  'assets/template/LeadMigrateReferralTemplate.xlsx';
export const LEAD_MIGRATE_REFFERAL_ADV_EXCEL_TEMPLATE =
  'assets/template/LeadMigrateReferralTemplate_Adv.xlsx';
export const DATA_MIGRATE_ADV_EXCEL_TEMPLATE =
  'assets/template/DataMigrateTemplate_Adv.xlsx';
export const DATA_ADV_EXCEL_TEMPLATE =
  'assets/template/DataTemplate_Adv.xlsx';
export const AGENCY_EXCEL_TEMPLATE = 'assets/template/AgencyTemplate.xlsx';
export const CAMPAIGN_EXCEL_TEMPLATE = 'assets/template/CampaignTemplate.xlsx';
export const CHANNELPARTNER_EXCEL_TEMPLATE =
  'assets/template/ChannelPartnerTemplate.xlsx';
export const LISTING_EXCEL_TEMPLATE = 'assets/template/ListingTemplate.xlsx';
export const ADDRESS_EXCEL_TEMPLATE = 'assets/template/AddressTemplate.xlsx';

export const FACING = [
  { displayName: 'East', value: 'East' },
  { displayName: 'West', value: 'West' },
  { displayName: 'North', value: 'North' },
  { displayName: 'South', value: 'South' },
  { displayName: 'North-East', value: 'NorthEast' },
  { displayName: 'North-West', value: 'NorthWest' },
  { displayName: 'South-East', value: 'SouthEast' },
  { displayName: 'South-West', value: 'SouthWest' },
];
export const PROPERTY_STATUS = ['Active', 'Sold'];
export const TASK_FILTER_TYPES = [
  {
    name: 'Today',
    bg: 'bg-linear-gradient-purple',
    icon: 'ic-find',
    color: 'text-purple',
  },
  {
    name: 'Upcoming',
    bg: 'bg-linear-gradient-green',
    icon: 'ic-reload',
    color: 'text-green-900',
  },
  {
    name: 'Overdue',
    bg: 'bg-linear-gradient-red',
    icon: 'ic-alert',
    color: 'text-red',
  },
  {
    name: 'Completed',
    bg: 'bg-linear-gradient-orange',
    icon: 'ic-completed',
    color: 'text-orange',
  },
  {
    name: 'All',
    bg: 'bg-linear-gradient-aqua',
    icon: 'ic-calendar',
  },
];

export const TASK_PRIORITIES = [
  { name: 'critical', value: 3 },
  { name: 'high', value: 2 },
  { name: 'medium', value: 1 },
  { name: 'low', value: 0 },
];

export const PROPERTY_TYPE_LIST = {
  Residential: 'assets/images/residential.svg',
  Commercial: 'assets/images/commercial.svg',
  Agricultural: 'assets/images/agricultural.svg',
};

export const TRANSACTION_TYPE_LIST = [
  {
    type: 'Buy',
    img: 'assets/images/buy.svg',
  },
  {
    type: 'Rent',
    img: 'assets/images/rent.svg',
  },
  {
    type: 'Sale',
    img: 'assets/images/sale.svg',
  },
];

export const SALE_TYPE_LIST = [
  {
    displayName: 'New',
    img: 'assets/images/new.svg',
  },
  {
    displayName: 'Resale',
    img: 'assets/images/resale.svg',
  },
];

export const BHK_TYPE = ['Simplex', 'Duplex', 'PentHouse', 'Others'];
export const BHK_NO = [
  '0.5',
  '1',
  '1.5',
  '2',
  '2.5',
  '3',
  '3.5',
  '4',
  '4.5',
  '5',
  '5+',
];

export const BR_NO = ['0.5', '1', '2', '3', '4', '5', '5+'];

export const BHK_NO_ALL = [
  '0.5',
  '1',
  '1.5',
  '2',
  '2.5',
  '3',
  '3.5',
  '4',
  '4.5',
  '5',
  '5.5',
  '6',
  '6.5',
  '7',
  '7.5',
  '8',
  '8.5',
  '9',
  '9.5',
  '10',
];

export const BR_NO_ALL = [
  '0.5',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
];

export const NUMBER_5 = [
  { display: '1', value: '1' },
  { display: '2', value: '2' },
  { display: '3', value: '3' },
  { display: '4', value: '4' },
  { display: '5+', value: '5' },
];

export const FLOOR_OPTIONS = [
  { display: 'Ground Floor', value: 'Ground Floor' },
  { display: '1', value: '1' },
  { display: '2', value: '2' },
  { display: '3', value: '3' },
  { display: '4', value: '4' },
  { display: '5+', value: '5' },
];

export const NUMBER_10 = [
  { display: 'Studio', value: 0 },
  { display: '1', value: 1 },
  { display: '2', value: 2 },
  { display: '3', value: 3 },
  { display: '4', value: 4 },
  { display: '5', value: 5 },
  { display: '6', value: 6 },
  { display: '7', value: 7 },
  { display: '8', value: 8 },
  { display: '9', value: 9 },
  { display: '10', value: 10 },
];

export const ASSIGN_TO = ['Individual', 'Team'];

export const DEFAULT_LOADING_TEXT = 'Layback Time';
export const PROPERTY_TYPE_IMAGES = {
  residential: 'assets/images/residential.svg',
  commercial: 'assets/images/commercial.svg',
  agricultural: 'assets/images/agricultural.svg',
};
export const ATTRIBUTES_MAP = [
  {
    name: 'numberOfKitchens',
    singularShortName: 'Kitchen',
    pluralShortName: 'Kitchens',
    displayName: 'No. of Kitchens',
    icon: 'ic-kitchen',
  },
  {
    name: 'numberOfBedrooms',
    singularShortName: 'Bed Room',
    pluralShortName: 'Bed Rooms',
    displayName: 'No. of Bed Rooms',
    icon: 'ic-bed',
  },
  {
    name: 'numberOfUtilities',
    singularShortName: 'Utility',
    pluralShortName: 'Utilities',
    displayName: 'No. of Utilities',
    icon: 'ic-utility',
  },
  {
    name: 'numberOfBathrooms',
    singularShortName: 'Bath Room',
    pluralShortName: 'Bath Rooms',
    displayName: 'No. of Bath Rooms',
    icon: 'ic-bath-tub',
  },
  {
    name: 'numberOfBalconies',
    singularShortName: 'Balcony',
    pluralShortName: 'Balconies',
    displayName: 'No. of Balconies',
    icon: 'ic-balcony',
  },
  {
    name: 'numberOfDrawingOrLivingRooms',
    singularShortName: 'Living Room',
    pluralShortName: 'Living Rooms',
    displayName: 'No. of Drawing or Living Rooms',
    icon: 'ic-sofa-lamp',
  },
  {
    name: 'maximumOccupants',
    singularShortName: 'Maximum Occupancy',
    pluralShortName: 'Maximum Occupancy',
    displayName: 'Maximum Occupancy',
    icon: 'ic-people',
  },
  {
    name: 'numberOfParking',
    singularShortName: 'Parking',
    pluralShortName: 'Parkings',
    displayName: 'Parking',
    icon: 'ic-car-parking',
  },
];

export const ExcelBulkLead = [
  {
    displayName: 'Name',
    mappingControlName: 'Name',
  },
  {
    displayName: 'Primary Number',
    mappingControlName: 'ContactNo',
  },
  {
    displayName: 'Primary No Country Code',
    mappingControlName: 'CountryCode',
  },
  {
    displayName: 'Alternate Number',
    mappingControlName: 'AlternateContactNo',
  },
  {
    displayName: 'Alternate No Country Code',
    mappingControlName: 'AlternativeNoCountryCode',
  },
  {
    displayName: 'Landline Number',
    mappingControlName: 'Landline',
  },
  {
    displayName: 'Email',
    mappingControlName: 'Email',
  },
  {
    displayName: 'Gender',
    mappingControlName: 'Gender',
  },
  {
    displayName: 'Marital Status',
    mappingControlName: 'MaritalStatus',
  },
  {
    displayName: 'Date Of Birth (dd/mm/yyyy)',
    mappingControlName: 'DateOfBirth',
  },
  {
    displayName: 'Referral Name',
    mappingControlName: 'ReferralName',
  },
  {
    displayName: 'Referral Email',
    mappingControlName: 'ReferralEmail',
  },
  {
    displayName: 'Referral Phone No',
    mappingControlName: 'ReferralContactNo',
  },
  {
    displayName: 'Source',
    mappingControlName: 'Source',
  },
  {
    displayName: 'Sub-Source',
    mappingControlName: 'SubSource',
  },
  {
    displayName: 'Primary Owner',
    mappingControlName: 'AssignToUser',
  },
  {
    displayName: 'Secondary Owner',
    mappingControlName: 'AssignToSecondaryUser',
  },
  {
    displayName: 'Nationality',
    mappingControlName: 'Nationality',
  },
  {
    displayName: 'Customer Location',
    mappingControlName: 'CustomerLocation',
  },
  {
    displayName: 'Customer SubCommunity',
    mappingControlName: 'CustomerSubCommunity',
  },
  {
    displayName: 'Customer Community',
    mappingControlName: 'CustomerCommunity',
  },
  {
    displayName: 'Customer TowerName',
    mappingControlName: 'CustomerTowerName',
  },
  {
    displayName: 'Customer City',
    mappingControlName: 'CustomerCity',
  },
  {
    displayName: 'Customer State',
    mappingControlName: 'CustomerState',
  },
  {
    displayName: 'Customer Country',
    mappingControlName: 'CustomerCountry',
  },
  {
    displayName: 'Customer PostalCode',
    mappingControlName: 'CustomerPostalCode',
  },
  {
    displayName: 'Currency',
    mappingControlName: 'Currency',
  },
  {
    displayName: 'Min. Budget',
    mappingControlName: 'LowerBudget',
  },
  {
    displayName: 'Max. Budget',
    mappingControlName: 'UpperBudget',
  },
  {
    displayName: 'Carpet Area',
    mappingControlName: 'CarpetArea',
  },
  {
    displayName: 'Carpet Area Unit',
    mappingControlName: 'CarpetAreaUnit',
  },
  {
    displayName: 'Saleable Area',
    mappingControlName: 'SaleableArea',
  },
  {
    displayName: 'Saleable Area Unit',
    mappingControlName: 'SaleableAreaUnit',
  },
  {
    displayName: 'BuiltUp Area',
    mappingControlName: 'BuiltUpArea',
  },
  {
    displayName: 'BuiltUp Area Unit',
    mappingControlName: 'BuiltUpAreaUnit',
  },
  {
    displayName: 'Property Area',
    mappingControlName: 'PropertyArea',
  },
  {
    displayName: 'Property Area Unit',
    mappingControlName: 'PropertyAreaUnit',
  },
  {
    displayName: 'Net Area',
    mappingControlName: 'NetArea',
  },
  {
    displayName: 'Net Area Unit',
    mappingControlName: 'NetAreaUnit',
  },
  {
    displayName: 'Unit Number/Name',
    mappingControlName: 'UnitName',
  },
  {
    displayName: 'Cluster Name',
    mappingControlName: 'ClusterName',
  },
  {
    displayName: 'Purpose',
    mappingControlName: 'Purpose',
  },
  {
    displayName: 'Enquired For',
    mappingControlName: 'EnquiredFor',
  },
  {
    displayName: 'Property Type',
    mappingControlName: 'BasePropertyType',
  },
  {
    displayName: 'Property Subtype',
    mappingControlName: 'SubPropertyType',
  },
  {
    displayName: 'BHK',
    mappingControlName: 'NoOfBHK',
  },
  {
    displayName: 'BHKType',
    mappingControlName: 'BHKType',
  },
  /* BR field commented out
  {
    displayName: 'BR',
    mappingControlName: 'NoOfBHK',
  },
  */
  {
    displayName: 'Beds',
    mappingControlName: 'Beds',
  },
  {
    displayName: 'Baths',
    mappingControlName: 'Baths',
  },
  {
    displayName: 'Furnish Status',
    mappingControlName: 'FurnishStatus',
  },
  {
    displayName: 'Preferred Floor',
    mappingControlName: 'PreferredFloor',
  },
  {
    displayName: 'Project',
    mappingControlName: 'Project',
  },
  {
    displayName: 'Property',
    mappingControlName: 'Property',
  },
  {
    displayName: 'Offering Type',
    mappingControlName: 'OfferingType',
  },
  {
    displayName: 'Agency Name',
    mappingControlName: 'AgencyName',
  },
  {
    displayName: 'Campaign Name',
    mappingControlName: 'CampaignName',
  },
  {
    displayName: 'Channel Partner',
    mappingControlName: 'ChannelPartnerName',
  },
  {
    displayName: 'Possession Type',
    mappingControlName: 'PossesionType',
  },
  {
    displayName: 'Possession Needed By(DD/MM/YYYY)',
    mappingControlName: 'PossessionDate',
  },
  {
    displayName: 'Enquired Locality',
    mappingControlName: 'Location',
  },
  {
    displayName: 'Enquired Sub-Community',
    mappingControlName: 'SubCommunity',
  },
  {
    displayName: 'Enquired Community',
    mappingControlName: 'Community',
  },
  {
    displayName: 'Enquired Tower Name',
    mappingControlName: 'TowerName',
  },
  {
    displayName: 'Enquired City',
    mappingControlName: 'City',
  },
  {
    displayName: 'Enquired State',
    mappingControlName: 'State',
  },
  {
    displayName: 'Enquired Country',
    mappingControlName: 'Country',
  },
  {
    displayName: 'Enquired Pin Code',
    mappingControlName: 'PostalCode',
  },
  {
    displayName: 'Profession',
    mappingControlName: 'Profession',
  },
  {
    displayName: 'Company Name',
    mappingControlName: 'CompanyName',
  },
  {
    displayName: 'Designation',
    mappingControlName: 'Designation',
  },
  {
    displayName: 'Sourcing Manager',
    mappingControlName: 'SourcingManager',
  },
  {
    displayName: 'Closing Manager',
    mappingControlName: 'ClosingManager',
  },
  {
    displayName: 'Status',
    mappingControlName: 'BaseStatus',
  },
  {
    displayName: 'Sub-Status',
    mappingControlName: 'SubStatus',
  },
  {
    displayName: 'Scheduled Date (DD/MM/YYYY HH:MM TT)',
    mappingControlName: 'ScheduledDate',
  },
  {
    displayName: 'Notes',
    mappingControlName: 'Notes',
  },
];

export const ExcelBulkLeadMigration = [
  {
    displayName: 'Name',
    mappingControlName: 'Name',
  },
  {
    displayName: 'Primary Number',
    mappingControlName: 'ContactNo',
  },
  {
    displayName: 'Primary No Country Code',
    mappingControlName: 'CountryCode',
  },
  {
    displayName: 'Alternate Number',
    mappingControlName: 'AlternateContactNo',
  },
  {
    displayName: 'Alternate No Country Code',
    mappingControlName: 'AlternativeNoCountryCode',
  },
  {
    displayName: 'Landline Number',
    mappingControlName: 'Landline',
  },
  {
    displayName: 'Created Date (DD/MM/YYYY HH:MM TT)',
    mappingControlName: 'CreatedDate',
  },
  {
    displayName: 'Email',
    mappingControlName: 'Email',
  },
  {
    displayName: 'Gender',
    mappingControlName: 'Gender',
  },
  {
    displayName: 'Marital Status',
    mappingControlName: 'MaritalStatus',
  },
  {
    displayName: 'Date Of Birth (dd/mm/yyyy)',
    mappingControlName: 'DateOfBirth',
  },
  {
    displayName: 'Referral Name',
    mappingControlName: 'ReferralName',
  },
  {
    displayName: 'Referral Phone No',
    mappingControlName: 'ReferralContactNo',
  },
  {
    displayName: 'Referral Email',
    mappingControlName: 'ReferralEmail',
  },
  {
    displayName: 'Source',
    mappingControlName: 'Source',
  },
  {
    displayName: 'Sub-Source',
    mappingControlName: 'SubSource',
  },
  {
    displayName: 'Primary Owner',
    mappingControlName: 'AssignToUser',
  },
  {
    displayName: 'Secondary Owner',
    mappingControlName: 'AssignToSecondaryUser',
  },
  {
    displayName: 'Nationality',
    mappingControlName: 'Nationality',
  },
  {
    displayName: 'Customer Location',
    mappingControlName: 'CustomerLocation',
  },
  {
    displayName: 'Customer SubCommunity',
    mappingControlName: 'CustomerSubCommunity',
  },
  {
    displayName: 'Customer Community',
    mappingControlName: 'CustomerCommunity',
  },
  {
    displayName: 'Customer TowerName',
    mappingControlName: 'CustomerTowerName',
  },
  {
    displayName: 'Customer City',
    mappingControlName: 'CustomerCity',
  },
  {
    displayName: 'Customer State',
    mappingControlName: 'CustomerState',
  },
  {
    displayName: 'Customer Country',
    mappingControlName: 'CustomerCountry',
  },
  {
    displayName: 'Customer PostalCode',
    mappingControlName: 'CustomerPostalCode',
  },
  {
    displayName: 'Currency',
    mappingControlName: 'Currency',
  },
  {
    displayName: 'Min. Budget',
    mappingControlName: 'LowerBudget',
  },
  {
    displayName: 'Max. Budget',
    mappingControlName: 'UpperBudget',
  },
  {
    displayName: 'Status',
    mappingControlName: 'BaseStatus',
  },
  {
    displayName: 'Sub-Status',
    mappingControlName: 'SubStatus',
  },
  {
    displayName: 'Scheduled Date (DD/MM/YYYY HH:MM TT)',
    mappingControlName: 'ScheduledDate',
  },
  {
    displayName: 'Site Visit Done (Count)',
    mappingControlName: 'SiteVisitDoneCount',
  },
  {
    displayName: 'Site Visit Not Done (Count)',
    mappingControlName: 'SiteVisitNotDoneCount',
  },
  {
    displayName: 'Meeting Done (Count)',
    mappingControlName: 'MeetingDoneCount',
  },
  {
    displayName: 'Meeting Not Done (Count)',
    mappingControlName: 'MeetingNotDoneCount',
  },
  {
    displayName: 'Carpet Area',
    mappingControlName: 'CarpetArea',
  },
  {
    displayName: 'Carpet Area Unit',
    mappingControlName: 'CarpetAreaUnit',
  },
  {
    displayName: 'Saleable Area',
    mappingControlName: 'SaleableArea',
  },
  {
    displayName: 'Saleable Area Unit',
    mappingControlName: 'SaleableAreaUnit',
  },
  {
    displayName: 'BuiltUp Area',
    mappingControlName: 'BuiltUpArea',
  },
  {
    displayName: 'BuiltUp Area Unit',
    mappingControlName: 'BuiltUpAreaUnit',
  },
  {
    displayName: 'Property Area',
    mappingControlName: 'PropertyArea',
  },
  {
    displayName: 'Property Area Unit',
    mappingControlName: 'PropertyAreaUnit',
  },
  {
    displayName: 'Net Area',
    mappingControlName: 'NetArea',
  },
  {
    displayName: 'Net Area Unit',
    mappingControlName: 'NetAreaUnit',
  },
  {
    displayName: 'Unit Number/Name',
    mappingControlName: 'UnitName',
  },
  {
    displayName: 'Cluster Name',
    mappingControlName: 'ClusterName',
  },
  {
    displayName: 'Purpose',
    mappingControlName: 'Purpose',
  },
  {
    displayName: 'Enquired For',
    mappingControlName: 'EnquiredFor',
  },
  {
    displayName: 'Property Type',
    mappingControlName: 'BasePropertyType',
  },
  {
    displayName: 'Property Subtype',
    mappingControlName: 'SubPropertyType',
  },
  {
    displayName: 'BHK',
    mappingControlName: 'NoOfBHK',
  },
  {
    displayName: 'BHK Type',
    mappingControlName: 'BHKType',
  },
  // {
  //   displayName: 'BR',
  //   mappingControlName: 'NoOfBHK',
  // },
  {
    displayName: 'Beds',
    mappingControlName: 'Beds',
  },
  {
    displayName: 'Baths',
    mappingControlName: 'Baths',
  },
  {
    displayName: 'Furnish Status',
    mappingControlName: 'FurnishStatus',
  },
  {
    displayName: 'Preferred Floor',
    mappingControlName: 'PreferredFloor',
  },
  {
    displayName: 'Offering Type',
    mappingControlName: 'OfferingType',
  },
  {
    displayName: 'Property',
    mappingControlName: 'Property',
  },
  {
    displayName: 'Project',
    mappingControlName: 'Project',
  },
  {
    displayName: 'Agency Name',
    mappingControlName: 'AgencyName',
  },
  {
    displayName: 'Campaign Name',
    mappingControlName: 'CampaignName',
  },
  {
    displayName: 'Channel Partner',
    mappingControlName: 'ChannelPartnerName',
  },
  {
    displayName: 'Possession Type',
    mappingControlName: 'PossesionType',
  },
  {
    displayName: 'Possession Needed By(DD/MM/YYYY)',
    mappingControlName: 'PossessionDate',
  },
  {
    displayName: 'Enquired Locality',
    mappingControlName: 'Location',
  },
  {
    displayName: 'Enquired Sub-Community',
    mappingControlName: 'SubCommunity',
  },
  {
    displayName: 'Enquired Community',
    mappingControlName: 'Community',
  },
  {
    displayName: 'Enquired Tower Name',
    mappingControlName: 'TowerName',
  },
  {
    displayName: 'Enquired Pin Code',
    mappingControlName: 'PostalCode',
  },
  {
    displayName: 'Enquired City',
    mappingControlName: 'City',
  },
  {
    displayName: 'Enquired State',
    mappingControlName: 'State',
  },
  {
    displayName: 'Enquired Country',
    mappingControlName: 'Country',
  },
  {
    displayName: 'Profession',
    mappingControlName: 'Profession',
  },
  {
    displayName: 'Company Name',
    mappingControlName: 'CompanyName',
  },
  {
    displayName: 'Designation',
    mappingControlName: 'Designation',
  },
  {
    displayName: 'Sourcing Manager',
    mappingControlName: 'SourcingManager',
  },
  {
    displayName: 'Closing Manager',
    mappingControlName: 'ClosingManager',
  },
  {
    displayName: 'Notes',
    mappingControlName: 'Notes',
  },
  {
    displayName: 'Tags',
    mappingControlName: 'Tag',
  },
];

export const ExcelBulkDataMigration = [
  {
    displayName: 'Name',
    mappingControlName: 'Name',
  },
  {
    displayName: 'Primary Number',
    mappingControlName: 'ContactNo',
  },
  {
    displayName: 'Primary No Country Code',
    mappingControlName: 'CountryCode',
  },
  {
    displayName: 'Alternate Number',
    mappingControlName: 'AlternateContactNo',
  },
  {
    displayName: 'Alternate No Country Code',
    mappingControlName: 'AlternativeNoCountryCode',
  },
  {
    displayName: 'Landline Number',
    mappingControlName: 'Landline',
  },
  {
    displayName: 'Created Date (DD/MM/YYYY HH:MM TT)',
    mappingControlName: 'CreatedDate',
  },
  {
    displayName: 'Email',
    mappingControlName: 'Email',
  },
  {
    displayName: 'Gender',
    mappingControlName: 'Gender',
  },
  {
    displayName: 'Marital Status',
    mappingControlName: 'MaritalStatus',
  },
  {
    displayName: 'Date Of Birth (dd/mm/yyyy)',
    mappingControlName: 'DateOfBirth',
  },
  {
    displayName: 'Referral Name',
    mappingControlName: 'ReferralName',
  },
  {
    displayName: 'Referral Phone No',
    mappingControlName: 'ReferralContactNo',
  },
  {
    displayName: 'Referral Email',
    mappingControlName: 'ReferralEmail',
  },
  {
    displayName: 'Source',
    mappingControlName: 'Source',
  },
  {
    displayName: 'Sub-Source',
    mappingControlName: 'SubSource',
  },
  {
    displayName: 'Assigned To (Username of user)',
    mappingControlName: 'AssignToUser',
  },
  {
    displayName: 'Nationality',
    mappingControlName: 'Nationality',
  },
  {
    displayName: 'Customer Location',
    mappingControlName: 'CustomerLocation',
  },
  {
    displayName: 'Customer SubCommunity',
    mappingControlName: 'CustomerSubCommunity',
  },
  {
    displayName: 'Customer Community',
    mappingControlName: 'CustomerCommunity',
  },
  {
    displayName: 'Customer TowerName',
    mappingControlName: 'CustomerTowerName',
  },
  {
    displayName: 'Customer City',
    mappingControlName: 'CustomerCity',
  },
  {
    displayName: 'Customer State',
    mappingControlName: 'CustomerState',
  },
  {
    displayName: 'Customer Country',
    mappingControlName: 'CustomerCountry',
  },
  {
    displayName: 'Customer PostalCode',
    mappingControlName: 'CustomerPostalCode',
  },
  {
    displayName: 'Currency',
    mappingControlName: 'Currency',
  },
  {
    displayName: 'Min. Budget',
    mappingControlName: 'LowerBudget',
  },
  {
    displayName: 'Max. Budget',
    mappingControlName: 'UpperBudget',
  },
  {
    displayName: 'Status',
    mappingControlName: 'BaseStatus',
  },
  {
    displayName: 'Scheduled Date (DD/MM/YYYY HH:MM TT)',
    mappingControlName: 'ScheduledDate',
  },
  {
    displayName: 'Carpet Area',
    mappingControlName: 'CarpetArea',
  },
  {
    displayName: 'Carpet Area Unit',
    mappingControlName: 'CarpetAreaUnit',
  },
  {
    displayName: 'Saleable Area',
    mappingControlName: 'SaleableArea',
  },
  {
    displayName: 'Saleable Area Unit',
    mappingControlName: 'SaleableAreaUnit',
  },
  {
    displayName: 'BuiltUp Area',
    mappingControlName: 'BuiltUpArea',
  },
  {
    displayName: 'BuiltUp Area Unit',
    mappingControlName: 'BuiltUpAreaUnit',
  },
  {
    displayName: 'Property Area',
    mappingControlName: 'PropertyArea',
  },
  {
    displayName: 'Property Area Unit',
    mappingControlName: 'PropertyAreaUnit',
  },
  {
    displayName: 'Net Area',
    mappingControlName: 'NetArea',
  },
  {
    displayName: 'Net Area Unit',
    mappingControlName: 'NetAreaUnit',
  },
  {
    displayName: 'Unit Number/Name',
    mappingControlName: 'UnitName',
  },
  {
    displayName: 'Cluster Name',
    mappingControlName: 'ClusterName',
  },
  {
    displayName: 'Purpose',
    mappingControlName: 'Purpose',
  },
  {
    displayName: 'Enquired For',
    mappingControlName: 'EnquiryTypes',
  },
  {
    displayName: 'Property Type',
    mappingControlName: 'BasePropertyType',
  },
  {
    displayName: 'Property Subtype',
    mappingControlName: 'SubPropertyType',
  },
  {
    displayName: 'BHK',
    mappingControlName: 'BHKs',
  },
  {
    displayName: 'BHK Type',
    mappingControlName: 'BHKTypes',
  },
  // {
  //   displayName: 'BR',
  //   mappingControlName: 'BHKs',
  // },
  {
    displayName: 'Beds',
    mappingControlName: 'Beds',
  },
  {
    displayName: 'Baths',
    mappingControlName: 'Baths',
  },
  {
    displayName: 'Furnish Status',
    mappingControlName: 'FurnishStatus',
  },
  {
    displayName: 'Preferred Floor',
    mappingControlName: 'PreferredFloor',
  },
  {
    displayName: 'Offering Type',
    mappingControlName: 'OfferingType',
  },
  {
    displayName: 'Property',
    mappingControlName: 'Property',
  },
  {
    displayName: 'Project',
    mappingControlName: 'Project',
  },
  {
    displayName: 'Agency Name',
    mappingControlName: 'AgencyName',
  },
  {
    displayName: 'Campaign Name',
    mappingControlName: 'CampaignName',
  },
  {
    displayName: 'Channel Partner',
    mappingControlName: 'ChannelPartnerName',
  },
  {
    displayName: 'Possession Type',
    mappingControlName: 'PossesionType',
  },
  {
    displayName: 'Possession Needed By(DD/MM/YYYY)',
    mappingControlName: 'PossessionDate',
  },
  {
    displayName: 'Enquired Locality',
    mappingControlName: 'Location',
  },
  {
    displayName: 'Enquired Sub-Community',
    mappingControlName: 'SubCommunity',
  },
  {
    displayName: 'Enquired Community',
    mappingControlName: 'Community',
  },
  {
    displayName: 'Enquired Tower Name',
    mappingControlName: 'TowerName',
  },
  {
    displayName: 'Enquired Pin Code',
    mappingControlName: 'PostalCode',
  },
  {
    displayName: 'Enquired City',
    mappingControlName: 'City',
  },
  {
    displayName: 'Enquired State',
    mappingControlName: 'State',
  },
  {
    displayName: 'Enquired Country',
    mappingControlName: 'Country',
  },
  {
    displayName: 'Profession',
    mappingControlName: 'Profession',
  },
  {
    displayName: 'Company Name',
    mappingControlName: 'CompanyName',
  },
  {
    displayName: 'Designation',
    mappingControlName: 'Designation',
  },
  {
    displayName: 'Sourcing Manager',
    mappingControlName: 'SourcingManager',
  },
  {
    displayName: 'Closing Manager',
    mappingControlName: 'ClosingManager',
  },

  {
    displayName: 'Notes',
    mappingControlName: 'Notes',
  },
];

export const ExcelBulkData = [
  {
    displayName: 'Name',
    mappingControlName: 'Name',
  },
  {
    displayName: 'Primary Number',
    mappingControlName: 'ContactNo',
  },
  {
    displayName: 'Primary No Country Code',
    mappingControlName: 'CountryCode',
  },
  {
    displayName: 'Alternate Number',
    mappingControlName: 'AltContactNo',
  },
  {
    displayName: 'Alternate No Country Code',
    mappingControlName: 'AlternativeNoCountryCode',
  },
  {
    displayName: 'Landline Number',
    mappingControlName: 'Landline',
  },
  {
    displayName: 'Email',
    mappingControlName: 'Email',
  },
  {
    displayName: 'Gender',
    mappingControlName: 'Gender',
  },
  {
    displayName: 'Marital Status',
    mappingControlName: 'MaritalStatus',
  },
  {
    displayName: 'Date Of Birth (dd/mm/yyyy)',
    mappingControlName: 'DateOfBirth',
  },
  {
    displayName: 'Referral Name',
    mappingControlName: 'ReferralName',
  },
  {
    displayName: 'Referral Email',
    mappingControlName: 'ReferralEmail',
  },
  {
    displayName: 'Referral Phone No',
    mappingControlName: 'ReferralContactNo',
  },
  {
    displayName: 'Source',
    mappingControlName: 'Source',
  },
  {
    displayName: 'Sub-Source',
    mappingControlName: 'SubSource',
  },
  {
    displayName: 'Nationality',
    mappingControlName: 'Nationality',
  },
  {
    displayName: 'Customer Location',
    mappingControlName: 'CustomerLocation',
  },
  {
    displayName: 'Customer SubCommunity',
    mappingControlName: 'CustomerSubCommunity',
  },
  {
    displayName: 'Customer Community',
    mappingControlName: 'CustomerCommunity',
  },
  {
    displayName: 'Customer TowerName',
    mappingControlName: 'CustomerTowerName',
  },
  {
    displayName: 'Customer City',
    mappingControlName: 'CustomerCity',
  },
  {
    displayName: 'Customer State',
    mappingControlName: 'CustomerState',
  },
  {
    displayName: 'Customer Country',
    mappingControlName: 'CustomerCountry',
  },
  {
    displayName: 'Customer PostalCode',
    mappingControlName: 'CustomerPostalCode',
  },
  {
    displayName: 'Currency',
    mappingControlName: 'Currency',
  },
  {
    displayName: 'Min. Budget',
    mappingControlName: 'LowerBudget',
  },
  {
    displayName: 'Max. Budget',
    mappingControlName: 'UpperBudget',
  },
  {
    displayName: 'Carpet Area',
    mappingControlName: 'CarpetArea',
  },
  {
    displayName: 'Carpet Area Unit',
    mappingControlName: 'CarpetAreaUnit',
  },
  {
    displayName: 'Saleable Area',
    mappingControlName: 'SaleableArea',
  },
  {
    displayName: 'Saleable Area Unit',
    mappingControlName: 'SaleableAreaUnit',
  },
  {
    displayName: 'BuiltUp Area',
    mappingControlName: 'BuiltUpArea',
  },
  {
    displayName: 'BuiltUp Area Unit',
    mappingControlName: 'BuiltUpAreaUnit',
  },
  {
    displayName: 'Property Area',
    mappingControlName: 'PropertyArea',
  },
  {
    displayName: 'Property Area Unit',
    mappingControlName: 'PropertyAreaUnit',
  },
  {
    displayName: 'Net Area',
    mappingControlName: 'NetArea',
  },
  {
    displayName: 'Net Area Unit',
    mappingControlName: 'NetAreaUnit',
  },
  {
    displayName: 'Unit Number/Name',
    mappingControlName: 'UnitName',
  },
  {
    displayName: 'Cluster Name',
    mappingControlName: 'ClusterName',
  },
  {
    displayName: 'Purpose',
    mappingControlName: 'Purpose',
  },
  {
    displayName: 'Enquired For',
    mappingControlName: 'EnquiryFor',
  },
  {
    displayName: 'Property Type',
    mappingControlName: 'BasePropertyType',
  },
  {
    displayName: 'Property Subtype',
    mappingControlName: 'SubPropertyType',
  },
  {
    displayName: 'BHK',
    mappingControlName: 'NoOfBHKs',
  },
  {
    displayName: 'BHKType',
    mappingControlName: 'BHKType',
  },
  {
    displayName: 'Beds',
    mappingControlName: 'Beds',
  },
  {
    displayName: 'Baths',
    mappingControlName: 'Baths',
  },
  {
    displayName: 'Furnish Status',
    mappingControlName: 'FurnishStatus',
  },
  {
    displayName: 'Preferred Floor',
    mappingControlName: 'PreferredFloor',
  },
  {
    displayName: 'Project',
    mappingControlName: 'Project',
  },
  {
    displayName: 'Property',
    mappingControlName: 'Property',
  },
  {
    displayName: 'Offering Type',
    mappingControlName: 'OfferingType',
  },
  {
    displayName: 'Agency Name',
    mappingControlName: 'AgencyName',
  },
  {
    displayName: 'Campaign Name',
    mappingControlName: 'CampaignName',
  },
  {
    displayName: 'Channel Partner',
    mappingControlName: 'ChannelPartnerName',
  },
  {
    displayName: 'Possession Type',
    mappingControlName: 'PossesionType',
  },
  {
    displayName: 'Possession Needed By(DD/MM/YYYY)',
    mappingControlName: 'PossessionDate',
  },
  {
    displayName: 'Enquired Locality',
    mappingControlName: 'Location',
  },
  {
    displayName: 'Enquired Sub-Community',
    mappingControlName: 'SubCommunity',
  },
  {
    displayName: 'Enquired Community',
    mappingControlName: 'Community',
  },
  {
    displayName: 'Enquired Tower Name',
    mappingControlName: 'TowerName',
  },
  {
    displayName: 'Enquired City',
    mappingControlName: 'City',
  },
  {
    displayName: 'Enquired State',
    mappingControlName: 'State',
  },
  {
    displayName: 'Enquired Country',
    mappingControlName: 'Country',
  },
  {
    displayName: 'Enquired Pin Code',
    mappingControlName: 'PostalCode',
  },
  {
    displayName: 'Profession',
    mappingControlName: 'Profession',
  },
  {
    displayName: 'Company Name',
    mappingControlName: 'CompanyName',
  },
  {
    displayName: 'Designation',
    mappingControlName: 'Designation',
  },
  {
    displayName: 'Sourcing Manager',
    mappingControlName: 'SourcingManager',
  },
  {
    displayName: 'Closing Manager',
    mappingControlName: 'ClosingManager',
  },
  {
    displayName: 'Notes',
    mappingControlName: 'Notes',
  },
];

export const PropertyDataColumns = [
  { displayName: 'Title', value: 'title' },
  { displayName: 'EnquiredFor', value: 'enquiredFor' },
  { displayName: 'SaleType', value: 'saleType' },
  { displayName: 'Notes', value: 'notes' },
  { displayName: 'FurnishStatus', value: 'furnishStatus' },
  { displayName: 'Status', value: 'status' },
  { displayName: 'Rating', value: 'rating' },
  { displayName: 'BrokerageAmount', value: 'brokerage' },
  { displayName: 'BrokerageCurrency', value: 'brokerageUnit' },
  { displayName: 'PossessionDate', value: 'possessionDate' },
  { displayName: 'PossesionType', value: 'PossesionType' },
  { displayName: 'Facing', value: 'facing' },
  { displayName: 'NoOfBHK', value: 'noOfBHK' },
  { displayName: 'BHKType', value: 'bhkType' },
  { displayName: 'BasePropertyType', value: 'propertyType' },
  { displayName: 'SubPropertyType', value: 'propertySubType' },
  { displayName: 'AboutProperty', value: 'aboutProperty' },
  { displayName: 'TotalPrice', value: 'expectedPrice' },
  { displayName: 'Currency', value: 'currency' },
  { displayName: 'City', value: 'city' },
  { displayName: 'State', value: 'state' },
  { displayName: 'Country', value: 'country' },
  { displayName: 'Location', value: 'location' },
  { displayName: 'OwnerName', value: 'ownerName' },
  { displayName: 'CountryCode', value: 'countryCode' },
  { displayName: 'OwnerPhoneNumber', value: 'ownerPhone' },
  { displayName: 'OwnerAltContactNo', value: 'OwnerAltContactNo' },
  { displayName: 'OwnerEmail', value: 'ownerEmail' },
  { displayName: 'Balconies', value: 'numberOfBalconies' },
  { displayName: 'Bathrooms', value: 'numberOfBathrooms' },
  { displayName: 'TotalFloors', value: 'numberOfFloors' },
  { displayName: 'Bedrooms', value: 'numberOfBedrooms' },
  { displayName: 'Parking', value: 'parking' },
  { displayName: 'FloorNumber', value: 'floorNumber' },
  { displayName: 'Utilities', value: 'numberOfUtilities' },
  { displayName: 'Kitchen', value: 'numberOfKitchens' },
  {
    displayName: 'DrawingOrLivingRooms',
    value: 'numberOfDrawingOrLivingRooms',
  },
  { displayName: 'PropertySize', value: 'propertySize' },
  { displayName: 'AreaUnit', value: 'areaUnit' },
  { displayName: 'NetArea', value: 'netArea' },
  { displayName: 'NetAreaUnit', value: 'netAreaUnit' },
  { displayName: 'IsNegotiable', value: 'isNegotiable' },
  { displayName: 'Project', value: 'project' },
  { displayName: 'CoWorkingOperator', value: 'coWorkingOperator' },
  { displayName: 'SecurityDeposit', value: 'securityDeposit' },
  { displayName: 'SecurityDepositUnit', value: 'securityDepositUnit' },
  { displayName: 'LockInPeriod', value: 'lockInPeriod' },
  { displayName: 'NoticePeriod', value: 'noticePeriod' },
  { displayName: 'Escalation', value: 'escalation' },
  { displayName: 'TenantPOCName', value: 'tenantPOCName' },
  { displayName: 'TenantPOCDesignation', value: 'tenantPOCDesignation' },
  { displayName: 'TenantPOCNumber', value: 'tenantPOCPhone' },
  {
    displayName: 'TenantPOCNumberCountryCode',
    value: 'tenantPOCNumberCountryCode',
  },
  {
    displayName: 'CoWorkingOperatorPOCName',
    value: 'coWorkingOperatorPOCName',
  },
  {
    displayName: 'CoWorkingOperatorPOCNumberCountryCode',
    value: 'coWorkingOperatorPOCNumberCountryCode',
  },
  {
    displayName: 'CoWorkingOperatorPOCNumber',
    value: 'coWorkingOperatorPOCPhone',
  },
  { displayName: 'DLDPermitNumber', value: 'dldPermitNumber' },
  { displayName: 'DTCMPermit', value: 'dtcMPermit' },
  { displayName: 'OfferingType', value: 'offeringType' },
  { displayName: 'CompletionStatus', value: 'completionStatus' },
  { displayName: 'PaymentFrequency', value: 'paymentFrequency' },
  { displayName: 'Community', value: 'community' },
  { displayName: 'SubCommunity', value: 'subCommunity' },
  { displayName: 'TowerName', value: 'towerName' },
  { displayName: 'ListedByUser', value: 'listedByUser' },
  { displayName: 'ImageUrls', value: 'imageUrls' },
  { displayName: 'ListingPortal', value: 'listingPortal' },
  { displayName: 'IsListed', value: 'isListed' },
  { displayName: 'UaeEmirate', value: 'selectEmirates' },
  { displayName: 'FinishingType', value: 'finishingType' },
  { displayName: 'Age', value: 'propertyAge' },
  { displayName: 'DownPayment', value: 'downPayment' },
  { displayName: 'ComplianceType', value: 'complianceType' },
];

export const AddressDisplayColumns = [
  {
    index: 2,
    displayName: 'Tower Name',
    value: 'towerName',
  },
  {
    index: 3,
    displayName: 'Sub Community',
    value: 'subCommunity',
  },
  {
    index: 4,
    displayName: 'Community',
    value: 'community',
  },
  {
    index: 5,
    displayName: 'City',
    value: 'city',
  },
];

export const AddressDataColumns = [
  { displayName: 'TowerName', value: 'towerName' },
  { displayName: 'SubCommunity', value: 'subCommunity' },
  { displayName: 'Community', value: 'community' },
  { displayName: 'City', value: 'city' },
];

export const PropertyDisplayColumns = [
  {
    displayName: 'Looking To',
    value: 'enquiredFor',
  },
  // {
  //   displayName: 'New/ Resale',
  //   value: 'saleType',
  // },
  {
    displayName: 'Notes',
    value: 'notes',
  },
  {
    displayName: 'Furnish Status',
    value: 'furnishStatus',
  },
  {
    displayName: 'Status',
    value: 'status',
  },
  {
    displayName: 'Rating',
    value: 'rating',
  },
  {
    displayName: 'Brokerage Amount',
    value: 'brokerage',
  },
  {
    displayName: 'Brokerage Unit',
    value: 'brokerageUnit',
  },
  {
    displayName: 'Possession Needed By(DD/MM/YYYY)',
    value: 'possessionDate',
  },
  {
    displayName: 'Possession Type',
    value: 'PossesionType',
  },
  {
    displayName: 'Facing',
    value: 'facing',
  },
  {
    displayName: 'BHK',
    value: 'noOfBHK',
  },
  {
    displayName: 'BHK Type',
    value: 'bhkType',
  },
  {
    displayName: 'Property Type',
    value: 'propertyType',
  },
  {
    displayName: 'Property Sub Type',
    value: 'propertySubType',
  },
  {
    displayName: 'About Property',
    value: 'aboutProperty',
  },
  {
    displayName: 'Price',
    value: 'expectedPrice',
  },
  {
    displayName: 'Currency',
    value: 'currency',
  },
  {
    displayName: 'City',
    value: 'city',
  },
  {
    displayName: 'State',
    value: 'state',
  },
  {
    displayName: 'Country',
    value: 'country',
  },
  {
    displayName: 'Location',
    value: 'location',
  },
  {
    displayName: 'Owner/Builder Name',
    value: 'ownerName',
  },
  {
    displayName: 'Owner Number Country Code',
    value: 'countryCode',
  },
  {
    displayName: 'Owner/Builder Phone',
    value: 'ownerPhone',
  },
  {
    displayName: 'Owner/Builder Alternate Phone',
    value: 'OwnerAltContactNo',
  },
  {
    displayName: 'Owner/Builder Email',
    value: 'ownerEmail',
  },
  {
    displayName: 'Balconies',
    value: 'numberOfBalconies',
  },
  {
    displayName: 'Bath Rooms',
    value: 'numberOfBathrooms',
  },
  {
    displayName: 'Parking',
    value: 'parking',
  },
  {
    displayName: 'Total Floors',
    value: 'numberOfFloors',
  },
  {
    displayName: 'Bed Rooms',
    value: 'numberOfBedrooms',
  },
  {
    displayName: 'Floor Numbers',
    value: 'floorNumber',
  },
  {
    displayName: 'Utilities',
    value: 'numberOfUtilities',
  },
  {
    displayName: 'Kitchens',
    value: 'numberOfKitchens',
  },
  {
    displayName: 'Drawing or Living Rooms',
    value: 'numberOfDrawingOrLivingRooms',
  },
  {
    displayName: 'Property Area',
    value: 'propertySize',
  },
  {
    displayName: 'Property Unit',
    value: 'areaUnit',
  },
  {
    displayName: 'Negotiable',
    value: 'isNegotiable',
  },
  {
    displayName: 'Project',
    value: 'project',
  },
  {
    displayName: 'Co Working Operator',
    value: 'coWorkingOperator',
  },
  {
    displayName: 'Security Deposit',
    value: 'securityDeposit',
  },
  {
    displayName: 'Security Deposit Unit',
    value: 'securityDepositUnit',
  },
  {
    displayName: 'Lock In Period',
    value: 'lockInPeriod',
  },
  {
    displayName: 'Notice Period',
    value: 'noticePeriod',
  },
  {
    displayName: 'Escalation',
    value: 'escalation',
  },
  {
    displayName: 'Tenant POC Name',
    value: 'tenantPOCName',
  },
  {
    displayName: 'Tenant POC Designation',
    value: 'tenantPOCDesignation',
  },
  {
    displayName: 'Tenant POC Number Country Code',
    value: 'tenantPOCNumberCountryCode',
  },
  {
    displayName: 'Tenant POC Phone',
    value: 'tenantPOCPhone',
  },
  {
    displayName: 'Image Urls',
    value: 'imageUrls',
  },
  {
    displayName: 'Co Working Operator POC Name',
    value: 'coWorkingOperatorPOCName',
  },
  {
    displayName: 'Co Working Operator POC Number Country Code',
    value: 'coWorkingOperatorPOCNumberCountryCode',
  },
  {
    displayName: 'Co Working Operator POC Phone',
    value: 'coWorkingOperatorPOCPhone',
  },
  {
    displayName: 'DLD Permit Number',
    value: 'dldPermitNumber',
  },
  {
    displayName: 'DTMC Permit Number',
    value: 'dtcMPermit',
  },
  {
    displayName: 'Offering Type',
    value: 'offeringType',
  },
  {
    displayName: 'Completion Status',
    value: 'completionStatus',
  },
  {
    displayName: 'Community',
    value: 'community',
  },
  {
    displayName: 'Sub Community',
    value: 'subCommunity',
  },
  {
    displayName: 'Tower Name',
    value: 'towerName',
  },
  {
    displayName: 'Payment Frequency',
    value: 'paymentFrequency',
  },
  {
    displayName: 'Net Area',
    value: 'netArea',
  },
  {
    displayName: 'Net Area Unit',
    value: 'netAreaUnit',
  },
  {
    displayName: 'Listed By User',
    value: 'listedByUser',
  },
  {
    displayName: 'Listing Portal',
    value: 'listingPortal',
  },
  {
    displayName: 'IsListed',
    value: 'isListed',
  },
];

export const ProjectDisplayColumns = [
  { index: 1, displayName: 'Project Name', value: 'Name' },
  { index: 2, displayName: 'Project Type', value: 'ProjectType' },
  { index: 3, displayName: 'Project Sub Type', value: 'ProjectSubType' },
  { index: 4, displayName: 'Project Status', value: 'ProjectStatus' },
  { index: 5, displayName: 'Land Area', value: 'LandArea' },
  { index: 6, displayName: 'Land Area Unit', value: 'AreaUnit' },
  { index: 7, displayName: 'Certificate', value: 'Certificate' },
  { index: 8, displayName: 'Facing', value: 'Facing' },
  { index: 9, displayName: 'Total Blocks', value: 'TotalBlocks' },
  { index: 10, displayName: 'Total Units', value: 'TotalUnits' },
  { index: 11, displayName: 'Total Floors', value: 'TotalFloors' },
  { index: 12, displayName: 'Description', value: 'Discription' },
  { index: 13, displayName: 'Builder Name', value: 'BuilderName' },
  { index: 14, displayName: 'Builder Contact Number Country Code', value: 'CountryCode' },
  { index: 15, displayName: 'Builder Contact Number', value: 'BuilderContactNumber' },
  { index: 16, displayName: 'Point of Contact Country Code', value: 'pointofContactcountrycode' },
  { index: 17, displayName: 'Point of Contact', value: 'PointofContact' },
  { index: 18, displayName: 'RERA Number', value: 'RERANumber' },
  { index: 19, displayName: 'Currency', value: 'Currency' },
  { index: 20, displayName: 'Min. Price', value: 'MinPrice' },
  { index: 21, displayName: 'Max. Price', value: 'MaxPrice' },
  { index: 22, displayName: 'Brokerage Amount', value: 'BrokerageAmount' },
  { index: 23, displayName: 'Brokerage Unit', value: 'BrokerageCurrency' },
  { index: 24, displayName: 'Possession Date Type', value: 'PossesionType' },
  { index: 25, displayName: 'Possession Date(DD/MM/YYYY)', value: 'PossessionDate' },
  { index: 26, displayName: 'Associated Banks', value: 'AssociatedBanks' },
  { index: 27, displayName: 'Locality', value: 'Location' },
  { index: 28, displayName: 'City', value: 'City' },
  { index: 29, displayName: 'State', value: 'State' },
  { index: 30, displayName: 'Amenities', value: 'Amenities' },
  { index: 31, displayName: 'Image Urls', value: 'ImageUrls' },
  { index: 32, displayName: 'Notes', value: 'Notes' },
];

export const ReferenceDisplayColumns = [
  {
    displayName: 'Property Title',
    value: 'propertyTitle',
  },
  {
    displayName: 'User Name',
    value: 'userName',
  },
  {
    displayName: 'Portal Name',
    value: 'portalName',
  },
  {
    displayName: 'Reference Id',
    value: 'referenceId',
  },

];

export const ReferenceMappingColumns = [
  { displayName: 'ReferenceId', value: 'referenceId' },
  { displayName: 'PropertyTitle', value: 'propertyTitle' },
  { displayName: 'PortalName', value: 'portalName' },
  { displayName: 'UserName', value: 'userName' },
];

export const ListingDisplayColumns = [
  {
    displayName: 'Select Emirates',
    value: 'selectEmirates',
  },
  {
    displayName: 'Offering Type',
    value: 'offeringType',
  },
  {
    displayName: 'Finishing Type',
    value: 'finishingType',
  },
  {
    displayName: 'Completion Status',
    value: 'completionStatus',
  },
  {
    displayName: 'Looking To',
    value: 'enquiredFor',
  },
  {
    displayName: 'Property Type',
    value: 'propertyType',
  },
  {
    displayName: 'Property Sub Type',
    value: 'propertySubType',
  },
  {
    displayName: 'BR',
    value: 'noOfBHK',
  },
  {
    displayName: 'Property Age',
    value: 'propertyAge',
  },
  {
    displayName: 'Listed By User',
    value: 'listedByUser',
  },
  {
    displayName: 'Property Area',
    value: 'propertySize',
  },
  {
    displayName: 'Property Unit',
    value: 'areaUnit',
  },
  {
    displayName: 'About Property',
    value: 'aboutProperty',
  },

  {
    displayName: 'Available From(DD/MM/YYYY)',
    value: 'possessionDate',
  },
  {
    displayName: 'Possession Type',
    value: 'PossesionType',
  },
  {
    displayName: 'Down Payment',
    value: 'downPayment',
  },
  {
    displayName: 'Price',
    value: 'expectedPrice',
  },
  {
    displayName: 'Payment Frequency',
    value: 'paymentFrequency',
  },
  {
    displayName: 'Currency',
    value: 'currency',
  },
  {
    displayName: 'City',
    value: 'city',
  },
  {
    displayName: 'State',
    value: 'state',
  },
  {
    displayName: 'Country',
    value: 'country',
  },
  {
    displayName: 'Location',
    value: 'location',
  },
  {
    displayName: 'Community',
    value: 'community',
  },
  {
    displayName: 'Sub Community',
    value: 'subCommunity',
  },
  {
    displayName: 'Tower Name',
    value: 'towerName',
  },
  {
    displayName: 'Notes',
    value: 'notes',
  },
  {
    displayName: 'Compliance Type',
    value: 'complianceType',
  },
  {
    displayName: 'RERA Permit Number',
    value: 'dldPermitNumber',
  },
  {
    displayName: 'DTMC Permit Number',
    value: 'dtcMPermit',
  },
  {
    displayName: 'Owner/Builder Name',
    value: 'ownerName',
  },
  {
    displayName: 'Owner Number Country Code',
    value: 'countryCode',
  },
  {
    displayName: 'Owner/Builder Phone',
    value: 'ownerPhone',
  },
  {
    displayName: 'Owner/Builder Alternate Phone',
    value: 'OwnerAltContactNo',
  },
  {
    displayName: 'Owner/Builder Email',
    value: 'ownerEmail',
  },
  {
    displayName: 'Furnish Status',
    value: 'furnishStatus',
  },
  {
    displayName: 'Status',
    value: 'status',
  },
  {
    displayName: 'Facing',
    value: 'facing',
  },
  {
    displayName: 'Bath Rooms',
    value: 'numberOfBathrooms',
  },
  {
    displayName: 'Parking',
    value: 'parking',
  },
  {
    displayName: 'Bed Rooms',
    value: 'numberOfBedrooms',
  },
  {
    displayName: 'Negotiable',
    value: 'isNegotiable',
  },
  {
    displayName: 'Image Urls',
    value: 'imageUrls',
  },
  {
    displayName: 'Listing Portal',
    value: 'listingPortal',
  },
  {
    displayName: 'IsListed',
    value: 'isListed',
  },
];

// ----------------------- Agency Bulk -----------------------
export const MarketingDataColumns = [
  { displayName: 'Name', value: 'agencyName', type: 'Agency' },
  { displayName: 'Name', value: 'channelPartnerName', type: 'ChannelPartner' },
  { displayName: 'Name', value: 'campaignName', type: 'Campaign' },
  { displayName: 'CountryCode', value: 'countryCode', type: 'All' },
  { displayName: 'PhoneNumber', value: 'phoneNumber', type: 'All' },
  { displayName: 'EmailId', value: 'email', type: 'All' },
  { displayName: 'Location', value: 'location', type: 'All' },
  { displayName: 'State', value: 'state', type: 'All' },
  { displayName: 'City', value: 'city', type: 'All' },
  { displayName: 'RERANumber', value: 'reraNumber', type: 'ChannelPartner' },
  { displayName: 'CompanyName', value: 'companyName', type: 'ChannelPartner' },
];

export const ChannelPartnerDisplayColumns = [
  {
    index: 3,
    displayName: 'Country Code',
    value: 'countryCode',
  },
  {
    index: 4,
    displayName: 'Phone Number',
    value: 'phoneNumber',
  },
  {
    index: 5,
    displayName: 'Email',
    value: 'email',
  },
  {
    index: 6,
    displayName: 'Locality',
    value: 'location',
  },
  {
    index: 7,
    displayName: 'City',
    value: 'city',
  },
  {
    index: 8,
    displayName: 'State',
    value: 'state',
  },
  {
    index: 9,
    displayName: 'RERA Number',
    value: 'reraNumber',
  },
  {
    index: 10,
    displayName: 'Company Name',
    value: 'companyName',
  },
];

export const AgencyDisplayColumns = [
  {
    index: 3,
    displayName: 'Country Code',
    value: 'countryCode',
  },
  {
    index: 4,
    displayName: 'Phone Number',
    value: 'phoneNumber',
  },
  {
    index: 5,
    displayName: 'Email',
    value: 'email',
  },
  {
    index: 6,
    displayName: 'Locality',
    value: 'location',
  },
  {
    index: 7,
    displayName: 'City',
    value: 'city',
  },
  {
    index: 8,
    displayName: 'State',
    value: 'state',
  },
];

export const ProjectUnitReqColumns = [
  {
    index: 1,
    displayName: 'Unit Name',
    controlName: 'name',
  },
  {
    index: 2,
    displayName: 'Unit Area in Sq.feet',
    controlName: 'unitArea',
  },
  {
    index: 3,
    displayName: 'Area Unit',
    controlName: 'areaUnit',
  },
];

export const ProjectUnitDisplayColumns = [
  {
    index: 4,
    displayName: 'Currency',
    controlName: 'currency',
  },
  {
    index: 5,
    displayName: 'Price Per Unit',
    controlName: 'pricePerUnit',
  },
  {
    index: 6,
    displayName: 'Total Price',
    controlName: 'totalPrice',
  },
  {
    index: 7,
    displayName: 'Unit Type',
    controlName: 'unitType',
  },
  {
    index: 8,
    displayName: 'Unit Sub Type',
    controlName: 'unitSubType',
  },
  {
    index: 9,
    displayName: 'Builtup Area in Sq.feet',
    controlName: 'builtupArea',
  },
  {
    index: 10,
    displayName: 'Builtup Area Unit',
    controlName: 'builtupAreaUnit',
  },
  {
    index: 11,
    displayName: 'Super Builtup Area in Sq.feet',
    controlName: 'superBuiltupArea',
  },
  {
    index: 12,
    displayName: 'Super Builtup Area Unit',
    controlName: 'superBuiltupAreaUnit',
  },
  {
    index: 13,
    displayName: 'Carpet Area in Sq.feet',
    controlName: 'carpetArea',
  },
  {
    index: 14,
    displayName: 'Carpet Area Unit',
    controlName: 'carpetAreaUnit',
  },
  {
    index: 15,
    displayName: 'BHK',
    controlName: 'noOfBhk',
  },
  {
    index: 16,
    displayName: 'BHK Type',
    controlName: 'bhkType',
  },
  {
    index: 17,
    displayName: 'Facing',
    controlName: 'facing',
  },
  {
    index: 18,
    displayName: 'Furnishing Status',
    controlName: 'furnishingStatus',
  },
  {
    index: 19,
    displayName: 'Maintenance Cost',
    controlName: 'maintenanceCost',
  },
  {
    index: 20,
    displayName: 'Balconies',
    controlName: 'numberOfBalconies',
  },
  {
    index: 21,
    displayName: 'Bath Rooms',
    controlName: 'numberOfBathrooms',
  },
  {
    index: 22,
    displayName: 'Bed Rooms',
    controlName: 'numberOfBedrooms',
  },
  {
    index: 23,
    displayName: 'Utilities',
    controlName: 'numberOfUtilities',
  },
  {
    index: 24,
    displayName: 'Kitchens',
    controlName: 'numberOfKitchens',
  },
  {
    index: 25,
    displayName: 'Drawing or Living Rooms',
    controlName: 'numberOfDrawingOrLivingRooms',
  },
  {
    index: 26,
    displayName: 'Maximum Occupants',
    controlName: 'maximumOccupants',
  },
];

export const ProjectUnitDataColumns = [
  { displayName: 'Name', value: 'name' },
  { displayName: 'UnitArea', value: 'unitArea' },
  { displayName: 'AreaUnit', value: 'areaUnit' },
  { displayName: 'CarpetArea', value: 'carpetArea' },
  { displayName: 'CarpetAreaUnit', value: 'carpetAreaUnit' },
  { displayName: 'BuiltupArea', value: 'builtupArea' },
  { displayName: 'BuiltupAreaUnit', value: 'builtupAreaUnit' },
  { displayName: 'SuperBuiltupArea', value: 'superBuiltupArea' },
  { displayName: 'SuperBuiltupAreaUnit', value: 'superBuiltupAreaUnit' },
  { displayName: 'PricePerUnit', value: 'pricePerUnit' },
  { displayName: 'TotalPrice', value: 'totalPrice' },
  { displayName: 'Currency', value: 'currency' },
  { displayName: 'UnitType', value: 'unitType' },
  { displayName: 'UnitSubType', value: 'unitSubType' },
  { displayName: 'NoOfBHK', value: 'noOfBhk' },
  { displayName: 'BHKType', value: 'bhkType' },
  { displayName: 'Facing', value: 'facing' },
  { displayName: 'FurnishingStatus', value: 'furnishingStatus' },
  { displayName: 'MaintenanceCost', value: 'maintenanceCost' },
  { displayName: 'Balconies', value: 'numberOfBalconies' },
  { displayName: 'BathRooms', value: 'numberOfBathrooms' },
  {
    displayName: 'DrawingOrLivingRooms',
    value: 'numberOfDrawingOrLivingRooms',
  },
  { displayName: 'BedRooms', value: 'numberOfBedrooms' },
  { displayName: 'Utilities', value: 'numberOfUtilities' },
  { displayName: 'Kitchens', value: 'numberOfKitchens' },
  { displayName: 'MaximumOccupants', value: 'maximumOccupants' },
];

export const RATING_LIST = [
  { index: 0, dispValue: 'Awesome - 5 stars', value: '5' },
  { index: 1, dispValue: 'Pretty good - 4 stars', value: '4' },
  { index: 2, dispValue: 'Meh - 3 stars', value: '3' },
  { index: 3, dispValue: 'Kinda bad - 2 stars', value: '2' },
  { index: 4, dispValue: 'Sucks big time - 1 star', value: '1' },
];

export const EXCEPTION_MESSAGES = {
  notAllowed: 'Not allowed to delete this role as its assigned to users.',
  adminUser: 'Administrators Profile status cannot be toggled.',
  dndNetwork: 'Number is Under DND/ Not Reachable/ Network Issue',
  agentBusy: 'Agents are Busy. Please try again after some time',
};

export const BLOOD_GROUP = [
  { id: 1, name: 'A+' },
  { id: 2, name: 'A-' },
  { id: 3, name: 'B+' },
  { id: 4, name: 'B-' },
  { id: 5, name: 'O+' },
  { id: 6, name: 'O-' },
  { id: 7, name: 'AB+' },
  { id: 8, name: 'AB-' },
];

export const GENDER = [
  { id: 1, type: 'Male', image: 'assets/images/mars.svg', bgColor: 'orange', displayName: 'Male' },
  { id: 2, type: 'Female', image: 'assets/images/venus.svg', bgColor: 'pink', displayName: 'Female' },
  {
    id: 3,
    type: 'prefer not to say',
    image: 'assets/images/transgender.svg',
    bgColor: 'yellow',
    displayName: 'Other',
  },
];

export const FILE_FORMAT = {
  excel: ['xls', 'xlx', 'csv', 'xlsx'],
  pdf: ['pdf'],
  image: ['jpeg', 'png', 'svg', 'jpg'],
  doc: ['doc', 'docx'],
  media: ['jpg', 'jpeg', 'png', 'pdf', 'mp4', 'gif'],
};

export const IVR_TYPE = [
  { name: 'Inbound', desc: 'This Include inbound data' },
  { name: 'Outbound', desc: 'This Include inbound & outbound data' },
];

export const SOCIAL_MEDIA = [
  {
    displayName: 'facebook',
    imagePath: 'assets/images/integration/facebook-logo.svg',
    baseUrl: 'https://www.facebook.com/',
  },
  {
    displayName: 'instagram',
    imagePath: 'assets/images/insta.svg',
    baseUrl: 'https://www.instagram.com/',
  },
  {
    displayName: 'twitter',
    imagePath: 'assets/images/twitter.svg',
    baseUrl: 'https://www.twitter.com/',
  },
  {
    displayName: 'linkedin',
    imagePath: 'assets/images/linkedIN.svg',
    baseUrl: 'https://www.linkedin.com/in/',
  },
  {
    displayName: 'whatsapp',
    imagePath: 'assets/images/integration/whatsapp-logo.svg',
    baseUrl: 'https://wa.me/',
  },
];

export const WEEK_DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export const MONTHS = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];

export const DASHBOARD_SOURCE_COLORS: any = {
  Direct: ['#50BEA7', '#E6F6F2'],
  IVR: ['#FF6209', '#FFF4EE'],
  Facebook: ['#3B5998', '#E3ECFF'],
  LinkedIn: ['#0072B1', '#E7F7FF'],
  GoogleAds: ['#F4B400', '#FFF6DD'],
  MagicBricks: ['#D90606', '#FFE6E6'],
  NinetyNineAcres: ['#0379DD', '#E5F1FC'],
  Housing: ['#7323DD', '#F4ECFF'],
  GharOffice: ['#EA2B72', '#FFD4E4'],
  Referral: ['#50BEA7', '#E6F6F2'],
  WalkIn: ['#50BEA7', '#E6F6F2'],
  Website: ['#50BEA7', '#E6F6F2'],
  Gmail: ['#BB001B', '#FFC3CC'],
  PropertyMicrosite: ['#50BEA7', '#E6F6F2'],
  PortfolioMicrosite: ['#50BEA7', '#E6F6F2'],
  Phonebook: ['#35E2FA', '#F0F8F9'],
  CallLogs: ['#35E2FA', '#F0F8F9'],
  LeadPool: ['#50BEA7', '#E6F6F2'],
};

export const PHONE_PREFERRED_COUNTRIES = ['in'];

export const DONE_STATUS_LIST = [
  'Meeting Done',
  'Meeting Not Done',
  'Site Visit Done',
  'Site Visit Not Done',
];

export const LEADS_FILTERS = [
  'All Leads',
  'Active Leads',
  'Not Interested',
  'Dropped',
  'Booked',
  'Booking Cancel',
  'Invoiced',
];
export const DATE_FILTERS = [
  'All',
  'Scheduled Today',
  'Scheduled Tomorrow',
  'Scheduled next 2 days',
  'Upcoming Schedules',
];
export const SCHEDULED_TODAY_LIST = ['Site visits', 'Meetings', 'Callbacks'];
export const USER_TYPE = ['Primary', 'Secondary', 'Both'];

export const GLOBAL_TEMPLATE_VARIABLES = [
  '#User Name#',
  '#User Mobile#',
  '#User Email#',
  '#Tenant Name#',
  '#Lead Name#',
];

export const LEAD_TEMPLATE_VARIABLES = [
  '#Lead Name#',
  '#Gender#',
  '#Marital Status#',
  '#Date of Birth#',
  '#Project Name#',
  '#Property Name#',
  '#Schedule Date#',
  '#Schedule Time#',
  '#Price Range#',
  '#Enquiry Type#',
  '#No Of BHK#',
  // '#BR#',
  '#Beds#',
  '#Baths#',
  '#Furnish Status#',
  '#Preferred Floor#',
  '#Offering Type#',
  '#BHK Type#',
  '#Enquired Location#',
  '#Lead Alternate Contact No#',
  '#Lead Contact No#',
  '#Lead Landline Number#',
  '#Lead Email#',
  '#Referral Name#',
  '#Referral Contact No#',
  '#Referral Email#',
  '#Company Name#',
  '#Closing Manager#',
  '#Sourcing Manager#',
  '#Channel Partner Name#',
  '#Custom Lead Status#',
  '#Designation#',
  '#Agencies#',
  '#Campaigns#',
  '#Lead Source#',
  '#Sub Source#',
  '#Lower Budget#',
  '#Upper Budget#',
  '#Property Type#',
  '#PropertySubType#',
  '#Carpet Area#',
  '#Possession Date#',
  '#Currency#',
  '#Project Microsite URL#',
  '#Property Microsite URL#',
  '#User Name#',
  '#User Mobile#',
  '#User Email#',
  '#Tenant Name#',
  '#Assign To#',
  '#Primary Owner#',
  '#Secondary Owner#',
  '#Property Area#',
  '#Net Area#',
  '#Saleable Area#',
  '#BuiltUp Area#',
  '#Unit Number or Name#',
  '#Nationality#',
  '#Cluster Name#',
  '#Purpose#',
];

export const PROPERTY_TEMPLATE_VARIABLES = [
  '#Property Status#',
  '#Enquired For#',
  '#Property Type#',
  '#Property Sub-Type#',
  '#No Of BHK#',
  '#BHK Type#',
  '#No Of BR#',
  // '#BR Type#',
  '#Title#',
  '#Property Area#',
  '#Carpet Area#',
  '#Built-up Area#',
  '#Saleable Area#',
  '#Dimension#',
  '#Sale Type#',
  '#Possession Date#',
  '#Total Price#',
  '#Brokerage#',
  '#Notes#',
  '#About Property#',
  '#Address#',
  '#Owner Name#',
  '#Owner Phone#',
  '#Owner Email#',
  '#Total Floors#',
  '#Floor Number#',
  '#No Of Bath Rooms#',
  '#No Of Bed Rooms#',
  '#No Of Kitchens#',
  '#No Of Utilities#',
  '#No Of Balconies#',
  '#Furnish Status#',
  '#Facing#',
  '#Microsite URL#',
  '#Property URL#',
  '#User Name#',
  '#User Mobile#',
  '#User Email#',
  '#Tenant Name#',
  '#No of Living Rooms#',
  '#No of Parking#',
  "#Net Area#",
  '#Lead Name#',
];

export const PROJECT_TEMPLATE_VARIABLES = [
  '#Project Type#',
  '#Project Sub-Type#',
  '#Name#',
  '#Land Area#',
  '#Possession Date#',
  '#Brokerage#',
  '#Address#',
  '#Builder Name#',
  '#Notes#',
  '#Builder Phone#',
  '#Builder Point Of Contact#',
  '#Facing#',
  '#Microsite URL#',
  '#Project URL#',
  '#User Name#',
  '#User Mobile#',
  '#User Email#',
  '#Tenant Name#',
  '#Lead Name#',
];

export const PROJECT_UNIT_TEMPLATE_VARIABLES = [
  '#UnitName#',
  '#UnitArea#',
  '#CarpetArea#',
  '#BuiltupArea#',
  '#SuperBuiltupArea#',
  '#MaintenanceCost#',
  '#PricePerUnit#',
  '#TotalPrice#',
  '#UnitType#',
  '#UnitSubType#',
  '#Bhk#',
  '#BhkType#',
  '#Facing#',
  '#FurnishingStatus#',
  '#ProjectType#',
  '#ProjectSubType#',
  '#Name#',
  '#LandArea#',
  '#PossessionDate#',
  '#Brokerage#',
  '#Address#',
  '#BuilderName#',
  '#BuilderPhone#',
  '#BuilderPointOfContact#',
  '#Facing#',
  '#MicrositeUrl#',
  '#userName#',
  '#userMobile#',
  '#userEmail#',
  '#tenantName#',
];

export const DASHBOARD_TYPE = [
  'My Dashboard',
  'My Team Dashboard',
  'Organization Dashboard',
];

export const DASHBOARD_VISIBILITY = [
  { displayName: 'My Dashboard', value: 0 },
  { displayName: 'My Team Dashboard', value: 1 },
  { displayName: 'Organization Dashboard', value: 2 },
];

export const dashboardUpcomingEvents = {
  callback: 'Callback Scheduled',
  new: 'New lead',
  site_visit_scheduled: 'Site Visit Scheduled',
  meeting_scheduled: 'Meeting Scheduled',
  to_schedule_site_visit: 'To Schedule Site Visit',
  to_schedule_a_meeting: 'To Schedule a meeting',
  different_requirements: 'Different Requirements',
  dropped: 'Dropped',
  not_interested: 'Not Interested',
  busy: 'Busy',
  booked: 'Booked',
  pending: 'Pending',
  plan_postponed: 'Plan Postponed',
  'wrong/invalid_no.': 'Wrong/Invalid No',
  not_reachable: 'Not Reachable',
  different_location: 'Different Location',
  unmatched_budget: 'Unmatched Budget',
  not_answered: 'Not Answered',
  need_more_info: 'Need More Info',
  purchased_from_others: 'Purchased From Others',
  not_looking: 'Not Looking',
};

export const upcomingEventsColors: any = {
  callback: 'border-left-blue-50',
  meeting_scheduled: 'border-left-orange-800',
  site_visit_scheduled: 'border-left-green-40',
};

export const IMAGES = {
  building: 'assets/images/building.svg',
  highlight: 'assets/images/highlight.svg',
  rightHighlightTag: 'assets/images/right-highlight-tag.svg',
  leftHighlightTag: 'assets/images/left-highlight-tag.svg',
  mail: 'assets/images/mail.svg',
  call: 'assets/images/call.svg',
  location: 'assets/images/location.svg',
  locateMe: 'assets/images/locate-me.svg',
  user: 'assets/images/user.svg',
  facebook: 'assets/images/integration/facebook.svg',
  linkedin: 'assets/images/linkedin.svg',
  twitter: 'assets/images/twitter.svg',
  instagram: 'assets/images/instagram.svg',
  facebookIcon: 'assets/images/facebook-icon.svg',
  linkedinIcon: 'assets/images/linkedin-icon.svg',
  twitterIcon: 'assets/images/twitter-icon.svg',
  instagramIcon: 'assets/images/instagram-icon.svg',
  sale: 'assets/images/home-sale.svg',
  customLoanService: 'assets/images/custom-service.svg',
  errorPage: 'assets/images/404error-page.svg',
  dummyProperty: 'assets/images/dummy-property.svg',
  musoProperty: 'assets/images/muso-property.svg',
  amenities: 'assets/images/amenities.svg',
  dotsCircle: 'assets/images/dots-circle.svg',
  jigJag: 'assets/images/jig-jag.svg',
  enquiryBtn: 'assets/images/enquiry-btn.svg',
  rentTag: 'assets/images/rent-tag.svg',
  buyTag: 'assets/images/buy-tag.svg',
  octagram: 'assets/images/octagram.svg',
  noAmenitiesFound: 'assets/images/no-amenities-found.svg',
  ellipse: 'assets/images/animation-ellipse.svg',
  hexa: 'assets/images/animation-hexa.svg',
  octa: 'assets/images/animation-octa.svg',
  polygon: 'assets/images/animation-polygon.svg',
  rectangle: 'assets/images/animation-rectangle.svg',
  rhombus: 'assets/images/animation-rhombus.svg',
  star: 'assets/images/animation-star.svg',
  triangle: 'assets/images/animation-triangle.svg',
};

export const ATTRIBUTES_ITEM = [
  'Total Floors',
  'Floor Number',
  'No. of Kitchens',
  'No. of Bed Rooms',
  'No. of Utilities',
  'No. of Bath Rooms',
  'No. of Balconies',
  'No. of Drawing or Living Rooms',
  'Maximum Occupancy',
];

export const ATTRIBUTES_KEY = [
  'Total Floors',
  'Floor Number',
  'Kitchens',
  'Bed Rooms',
  'Utilities',
  'Bath Rooms',
  'Balconies',
  'Drawing or Living Rooms',
  'Maximum Occupants',
  'Parking',
];

export const ErrorActionCodeDescription = {
  [ErrorActionCode.NoOp]: 'Do nothing.',
  [ErrorActionCode.StayOn]: 'Stay on the same screen.',
  [ErrorActionCode.ChangeRoute]: 'Navigate to any other screen, if needed.',
  [ErrorActionCode.FallBack]: 'Navigate back to the parent screen.',
  [ErrorActionCode.ReturnToHome]: 'Navigate to main/home screen.',
  [ErrorActionCode.Refresh]: 'Refresh token data.',
  [ErrorActionCode.Logout]: 'Log the user out.',
};

export const LEAD_FILTERS_KEY_LABEL = {
  assignTo: 'Assigned To',
  HistoryAssignedToIds: 'Assigned To',
  UserIds: 'Call Made By',
  AssignedFromIds: 'Assigned From',
  StatusIds: 'Status',
  StatusesIds: 'Status',
  SubStatusIds: 'Sub Status',
  SubStatusesIds: 'Sub Status',
  source: 'Source',
  Sources: 'Source',
  subSource: 'Sub Source',
  SubSources: 'Sub Source',
  enquiredFor: 'Enquired For',
  AgencyNames: 'Agency Name',
  channelPartnerName: 'Channel Partner Name',
  PropertyType: 'Property Type',
  PropertySubType: 'Property Sub-Type',
  NoOfBHKs: 'BHK',
  BHKTypes: 'BHK Type',
  Locations: 'Location',
  Projects: 'Project',
  Properties: 'Property',
  createdByIds: 'Created By',
  lastModifiedByIds: 'Last Modified By',
  archivedByIds: 'Deleted By',
  restoredByIds: 'Restored By',
  PossesionType: 'Possession',
  FromPossesionDate: 'From Date',
  ToPossesionDate: 'To Date',
  sourcingManagers: 'Sourcing Manager',
  closingManagers: 'Closing Manager',
  Profession: 'Profession',
  CarpetArea: 'Carpet Area',
  MinBudget: 'Min. Budget',
  MaxBudget: 'Max. Budget',
  ReferralName: 'Referral Name',
  ReferralContactNo: 'Referral Phone No',
  CompanyName: 'Company Name',
  MeetingOrVisitStatuses: 'Appointment Action',
  CarpetAreaUnitId: 'Carpet Area Unit',
  SecondaryUsers: 'Secondary Assigned To',
  AppointmentDoneByUserIds: 'Appointment Done By',
  bookedByIds: 'Booked By',
  FromDateForMeetingOrVisit: 'Appointment Date',
  CustomFlags: 'Tags',
  Cities: 'City',
  States: 'State',
  Localities: 'Locality',
  BookedUnderName: 'Booked Under Name',
  LowerAgreementLimit: 'Agreement Value',
  carParkingCharges: 'Car Parking Charges',
  AdditionalCharges: 'Add-on Charges',
  SoldPrice: 'Sold Price',
  paymentMode: 'Payment Mode',
  LowerDiscountLimit: 'Discount',
  RemainingAmount: 'Balance Amount',
  PaymentType: 'Payment',
  BrokerageCharges: 'Brokerage Charges',
  NetBrokerageAmount: 'Brokerage Amount',
  gst: 'GST',
  TotalBrokerage: 'Total Brokerage',
  EarnedBrokerage: 'Brokerage Earned',
  IsUntouched: 'IsUntouched',
  UploadTypeName: 'Excel Sheet',
  RadiusInKm: 'Radius',
  TextByIds: 'Text By',
  SecondaryFromIds: 'Secondary From',
  HistoryAssignedTo: 'History Assigned To',
  DoneBy: 'Done By',
  designationsId: 'Designation',
  AdditionalPropertiesKey: 'Facebook Property',
  AdditionalPropertiesValue: 'Value',
  DataConverted: 'Data Converted',
  QualifiedByIds: 'Qualified By',
  PropertyArea: 'Property Area',
  NetArea: 'Net Area',
  UnitName: 'Unit Number/Name',
  ConfidentialNotes: 'Confidential Notes',
  ShowOnlyParentLeads: 'Show Parent Leads',
  ChildLeadsCount: 'Child Leads Count',
  ShowPrimeLeads: 'Show Unique Leads',
  OriginalOwner: 'Original Owner',
  LeadType: 'Lead Type ',
  dateOfBirth: 'Date of Birth',
  genderTypes: 'Gender',
  maritalStatuses: 'Marital Status',
  CountryCode: 'Country Code',
  AltCountryCode: 'Alternate Country Code',
  ownerSelection: 'Owner Type',
  ReferralEmail: 'Referral Email',
  CallDirections: 'Call Direction',
  CallStatuses: 'Call Status',
  CallLogFromDate: 'Call Date',
};

export const DASHBOARD_FILTERS_KEY_LABEL = {
  GeneralManagerIds: 'General Manager',
  UserIds: 'User',
  Projects: 'Project',
};

export const GRAPH_COLORS: any = {
  dialed: '#0194E7',
  connected: '#B7FE9E',
  incoming: '#9BEDFF',
  notConnected: '#FFCBCC',
  missed: '#B10000',
  whatsApp: '#38DAB8',
  sms: '#FFF3A6',
  calls: '#5887FF',
  email: '#A682FF',
  siteVisitDone: '#38DAB8',
  siteVisitNotDone: '#FF686B',
  siteVisitOverdue: '#BC9BD6',
  meetingDone: '#FFDC00',
  meetingNotDone: '#FF686B',
  meetingOverdue: '#B10000',
  New: '#9ED2BE',
  Pending: '#F09872',
  Callback: '#95BDFF',
  'Meeting Scheduled': '#F4C760',
  'Site Visit Scheduled': '#95BD95',
  Overdue: '#FEA8A1',
  'Expression Of Interest': '#0A9396',
  Booked: '#BA94D1',
  'Booking Cancel': '#F45D5D',
  'Not Interested': '#7A9BB9',
  Dropped: '#BCBCBC',
};

export const DATE_FILTER_LIST = [
  { displayName: 'Till Date', value: 'TillDate' },
  { displayName: 'Today', value: 'Today' },
  { displayName: 'Yesterday', value: 'Yesterday' },
  { displayName: 'Last 7 Days', value: 'Last7Days' },
  { displayName: 'Current Month', value: 'CurrentMonth' },
  { displayName: 'Custom', value: 'Custom' },
];

export const DATE_TYPE_FILTER_LIST = [
  { displayName: 'All', value: 'All' },
  { displayName: 'Created', value: 'Created Date' },
  { displayName: 'Scheduled', value: 'Scheduled Date' },
  { displayName: 'Modified', value: 'Modified Date' },
];

export const OTP_RECEIVER = [
  { displayName: 'OTP to Admin', value: 1 },
  { displayName: 'OTP to Reporting manager', value: 2 },
  { displayName: 'OTP to user', value: 0 },
];

export const OTP_USER_OPTIONS = [
  { displayName: 'Select all', value: true },
  { displayName: 'Select Users', value: false },
];

//ContactType - enum
export const OTP_TYPE = [
  // { displayName: 'Send OTP via SMS', value: 3 },
  { displayName: 'Send OTP via Email', value: 2 },
  { displayName: 'Send OTP via WhatsApp', value: 0 },
];

export const DATA_FILTERS_KEY_LABEL = {
  AgencyNames: 'Agency Name',
  ChannelPartnerNames: 'Channel Partner Name',
  AssignTo: 'Assigned To',
  BHKTypes: 'BHK Type',
  CarpetArea: 'Carpet Area',
  CarpetAreaUnitId: 'Carpet Area Unit',
  SaleableArea: 'Saleable Area',
  SaleableAreaUnitId: 'Saleable Area Unit',
  BuiltUpArea: 'BuiltUp Area',
  BuiltUpAreaUnitId: 'BuiltUp Area Unit',
  EnquiryTypes: 'Enquired For',
  MaxBudget: 'Max. Budget',
  MinBudget: 'Min. Budget',
  NoOfBHKs: 'BHK',
  Projects: 'Project',
  Properties: 'Property',
  PropertySubType: 'Property Sub-Type',
  PropertyType: 'Property Type',
  Source: 'Source',
  StatusIds: 'Status',
  SubSources: 'Sub Source',
  Locations: 'Location',
  Cities: 'City',
  States: 'State',
  Countries: 'Country',
  SubCommunities: 'Sub-Community',
  PossesionType: 'Possession',
  Communities: 'Community',
  TowerNames: 'Tower Name',
  Localities: 'Locality',
  AssignedFromIds: 'Assigned From',
  CreatedByIds: 'Created By',
  LastModifiedByIds: 'Last Modified By',
  ConvertedByIds: 'Converted By',
  QualifiedByIds: 'Qualified By',
  DeletedByIds: 'Deleted By',
  RestoredByIds: 'Restored By',
  CampaignNames: 'Campaigns',
  ReferralName: 'Referral Name',
  ReferralContactNo: 'Referral Phone No',
  ReferralEmail: 'Referral Email',
  ClosingManagers: 'Closing Manager',
  SourcingManagers: 'Sourcing Manager',
  NetArea: 'Net Area',
  PropertyArea: 'Property Area',
  UnitName: 'Unit Number/Name',
  UploadTypeName: 'Excel Sheet',
  CountryCode: 'Country Code',
  AltCountryCode: 'Alternate Country Code',
  FromPossesionDate: 'Possession From Date',
  ToPossesionDate: 'Possession To Date',
  DateOfBirth: 'Date of Birth',
  GenderTypes: 'Gender',
  MaritalStatuses: 'Marital Status',
};

export const PROPERTY_FILTERS_KEY_LABEL: any = {
  carpetArea: 'Carpet Area',
  propertySizeArea: 'Property Area',
  buildUpArea: 'Build Up Area',
  saleableArea: 'Saleable Area',
  netArea: 'Net Area',
  title: 'Property Title',
  ownerDetails: 'Owner',
  propertySubType: 'Property Sub Type',
  priceRange: 'Budget',
  locations: 'Locality / Area',
  cities: 'City',
  assignedTo: 'Assigned To',
  UserIds: 'Lisiting By',
  ListingOnBehalf: 'Listing On Behalf',
  states: 'State',
  noOfBhks: 'BHK',
  bhkType: 'BHK Type',
  possessionDate: 'Possession Date',
  furnishStatus: 'Furnish Status',
  projects: 'Project',
  facing: 'Facing',
  noOfFloors: 'No. Of Floors',
  noOfBathrooms: 'No. Of Bathrooms',
  noOfLivingrooms: 'No. Of Living Rooms',
  noOfBedrooms: 'No. Of Bedrooms',
  noOfUtilities: 'No. Of Utilities',
  noOfKitchens: 'No. Of Kitchens',
  noOfBalconies: 'No. Of Balcony',
  maxBudget: 'Max. Budget',
  minBudget: 'Min. Budget',
  Amenities: 'Amenity',
  possessionFromDate: 'Possession From Date',
  possessionToDate: 'Possession To Date',
  enquiredFor: 'Enquired For',
  minPrice: 'Min. Price',
  maxPrice: 'Max. Price',
  noOfBHKs: 'No. Of BHK',
  noOfUtilites: 'No. Of Utilities',
  PossesionType: 'Possession',
  /* BR field commented out
  NoOfBHK: 'No.Of BR',
  BHKTypes: 'BR Type',
  */
  Communities: 'Community',
  SubCommunities: 'Sub-Community',
  PropertyStatus: 'Property Status',
  SerialNo: 'Reference No',
  MinLeadCount: 'Min Lead',
  MaxLeadCount: 'Max Lead',
  MinProspectCount: 'Min Data',
  MaxProspectCount: 'Max Data',
  possessionType: 'Possession',
  FromPossesionDate: 'Possession From Date',
  ToPossesionDate: 'Possession To Date',
  noOfParking: 'No. of Parking',
  Parking: 'No. of Parking',
  countries: 'Country',
  Countries: 'Country',
};

export const REPORT_FILTERS_KEY_LABEL: any = {
  users: 'User',
  date: 'Date',
  dateType: 'Date Type',
  projects: 'Project',
  sources: 'Source',
  sourceIds: 'Source',
  subSources: 'Sub Source',
  agencyNames: 'Agency',
  states: 'State',
  cities: 'City',
  Countries: 'Country',
  dateForMeetingOrVisit: 'Date For Meeting Or Visit',
  dateForCall: 'Date For Call',
  dateForSource: 'Date For Source',
  dateForSubSource: 'Date For Sub Source',
  dateForAgency: 'Date For Agency',
  dateForProject: 'Date For Project',
  generatingFrom: 'Lead Generating From',
  dateForLeadReceived: 'Date For Lead Received',
  UserIds: 'User',
  FromDate: 'Date',
  ownerSelection: 'Owner Type',
};

export const QR_DISPLAY_FIELDS = [
  {
    displayName: 'Gender',
    controlName: 'gender',
    placeholder: 'select gender',
    type: 'dropdown'
  },
  {
    displayName: 'Date Of Birth',
    controlName: 'dateOfBirth',
    placeholder: 'dd-mm-yyyy',
    type: 'text'
  },
  {
    displayName: 'Marital Status',
    controlName: 'maritalStatus',
    placeholder: 'select marital status',
    type: 'dropdown'
  },
  {
    displayName: 'Landline Number',
    controlName: 'landLine',
    placeholder: 'enter landline number',
    type: 'text'
  },
  {
    displayName: 'Referral Name',
    controlName: 'referralName',
    placeholder: 'enter referral name',
    type: 'text'
  },
  {
    displayName: 'Referral Phone No',
    controlName: 'referralContactNo',
    placeholder: 'enter referral Phone no ',
    type: 'text'
  },
  {
    displayName: 'Referral Email',
    controlName: 'referralEmail',
    placeholder: 'enter referral email',
    type: 'text'
  },
  {
    displayName: 'Customer Locality',
    controlName: 'customerLocality',
    placeholder: 'enter customer locality',
    type: 'text'
  },
  {
    displayName: 'Customer Sub-Community',
    controlName: 'customerSubCommunity',
    placeholder: 'enter customer sub-community',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Customer Community',
    controlName: 'customerCommunity',
    placeholder: 'enter customer community',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Customer Tower Name',
    controlName: 'customerTowerName',
    placeholder: 'enter customer tower name',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Customer City',
    controlName: 'customerCity',
    placeholder: 'enter customer city',
    type: 'text'
  },
  {
    displayName: 'Customer State',
    controlName: 'customerState',
    placeholder: 'enter customer state',
    type: 'text'
  },
  {
    displayName: 'Customer Country',
    controlName: 'customerCountry',
    placeholder: 'enter customer country ',
    type: 'text'
  },
  {
    displayName: 'Budget', controlName: 'budget', placeholder: 'enter budget', type: 'range'
  },
  {
    displayName: 'Carpet Area',
    controlName: 'carpetArea',
    placeholder: 'enter carpet area',
    type: 'number'
  },
  {
    displayName: 'Built-up Area',
    controlName: 'builtUpArea',
    placeholder: 'enter built-up area',
    type: 'number',
  },
  {
    displayName: 'Saleable Area',
    controlName: 'saleableArea',
    placeholder: 'enter saleable area',
    type: 'number'

  },
  {
    displayName: 'Property Area',
    controlName: 'propertyArea',
    placeholder: 'enter property area',
    type: 'number',
    customization: true
  },
  {
    displayName: 'Net Area',
    controlName: 'netArea',
    placeholder: 'enter net area',
    type: 'number',
    customization: true
  },
  {
    displayName: 'Unit Number/Name',
    controlName: 'unitName',
    placeholder: 'enter unit number/name',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Cluster Name',
    controlName: 'clusterName',
    placeholder: 'enter cluster name',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Purpose',
    controlName: 'purpose',
    type: 'dropdown',
    placeholder: 'select purpose',
  },
  {
    displayName: 'Enquired For',
    controlName: 'enquiredFor',
    type: 'dropdown',
    placeholder: 'select enquired for',
  },
  {
    displayName: 'Property Type',
    controlName: 'basePropertyType',
    type: 'dropdown',
    placeholder: 'select property type',
  },
  {
    displayName: 'Property Sub-Type',
    controlName: 'subPropertyType',
    type: 'dropdown',
    placeholder: 'select property sub-type',
  },

  {
    displayName: 'BHK', controlName: 'noOfBHK', placeholder: 'select BHK', type: 'dropdown',
    customization: false
  },
  {
    displayName: 'BHK Type',
    controlName: 'bhkType',
    placeholder: 'select BHK type',
    type: 'dropdown',
    customization: false
  },
  // {
  //   displayName: 'BR',
  //   controlName: 'noOfBHK',
  //   placeholder: 'select BR',
  //   type: 'dropdown',
  //   customization: true
  // },
  {
    displayName: 'Baths',
    controlName: 'baths',
    placeholder: 'select baths',
    type: 'dropdown',
    customization: true
  },
  {
    displayName: 'Beds',
    controlName: 'beds',
    placeholder: 'select beds',
    type: 'dropdown',
    customization: true
  },
  {
    displayName: 'Preferred Floors',
    controlName: 'preferredFloors',
    placeholder: 'select preferred floors',
    type: 'dropdown',
    customization: true
  },
  {
    displayName: 'Furnish Status',
    controlName: 'furnishStatus',
    placeholder: 'select furnish status',
    type: 'dropdown',
    customization: true
  },
  {
    displayName: 'Project',
    controlName: 'project',
    placeholder: 'select project',
    type: 'dropdown',
  },
  {
    displayName: 'Property',
    controlName: 'property',
    placeholder: 'enter property',
    type: 'text'
  },
  {
    displayName: 'Offering Type',
    controlName: 'offeringType',
    placeholder: 'select offering type',
    type: 'dropdown',
    customization: true
  },

  {
    displayName: 'Agency Name',
    controlName: 'agencyName',
    placeholder: 'enter agency name',
    type: 'dropdown'
  },
  {
    displayName: 'Campaigns',
    controlName: 'campaigns',
    placeholder: 'enter campaign name',
    type: 'dropdown'
  },
  {
    displayName: 'Channel Partner Name',
    controlName: 'channelPartnerName',
    placeholder: 'enter channel partner name',
    type: 'dropdown'
  },
  {
    displayName: 'Possession Needed By',
    controlName: 'possessionAvailability',
    placeholder: 'ex. 19/06/2024',
    type: 'text'
  },
  {
    displayName: 'Enquired Locality',
    controlName: 'locality',
    placeholder: 'enter locality',
    type: 'text'
  },
  {
    displayName: 'Enquired Sub-Community',
    controlName: 'enquiredSubCommunity',
    placeholder: 'enter enquired sub-community',
    type: 'text',
    customization: true

  },
  {
    displayName: 'Enquired Community',
    controlName: 'enquiredCommunity',
    placeholder: 'enter enquired community',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Enquired Tower Name',
    controlName: 'enquiredTowerName',
    placeholder: 'enter enquired tower name',
    type: 'text',
    customization: true
  },
  {
    displayName: 'Enquired City',
    controlName: 'city',
    placeholder: 'enter enquired city',
    type: 'text'
  },
  {
    displayName: 'Enquired State',
    controlName: 'state',
    placeholder: 'enter enquired state',
    type: 'text'
  },
  {
    displayName: 'Enquired Country',
    controlName: 'enquiredCountry',
    placeholder: 'enter enquired country',
    type: 'text'
  },
  {
    displayName: 'Profession',
    controlName: 'profession',
    placeholder: 'enter profession ',
    type: 'text'
  },
  {
    displayName: 'Company Name',
    controlName: 'companyName',
    placeholder: 'enter company name',
    type: 'text'
  },
  {
    displayName: 'Designation',
    controlName: 'designation',
    placeholder: 'enter designation',
    type: 'text'
  },
  {
    displayName: 'Nationality',
    controlName: 'nationality',
    placeholder: 'select nationality',
    type: 'dropdown',
    customization: true
  },
  {
    displayName: 'Notes',
    controlName: 'notes',
    placeholder: 'ex. I want to say............',
    type: 'notetext'
  },
];

export const LEADS_FIELDS = [
  {
    displayName: 'Enquired For',
    items: 'enquiredForList',
    data: 'true',
    placeholder: 'select',
    addTag: 'false',
  },
  {
    displayName: 'Property Type',
    items: 'propertyType',
    data: 'false',
    placeholder: 'select',
    addTag: 'false',
  },
  {
    displayName: 'Property Sub Type',
    items: 'propertyTypeList',
    data: 'false',
    placeholder: 'select',
    addTag: 'false',
  },
];

export const BASIC_INFO = [
  { displayName: 'Name', controlName: 'name', placeholder: 'ex. select' },
  {
    displayName: 'Primary No',
    controlName: 'contactNo',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Alternate No',
    controlName: 'alternateContactNo',
    placeholder: 'ex. select',
  },
  { displayName: 'Email', controlName: 'email', placeholder: 'ex. select' },
  {
    displayName: 'Referral Name',
    controlName: 'referralName',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Referral Email',
    controlName: 'referralEmail',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Referral Phone No',
    controlName: 'referralContactNo',
    placeholder: 'ex. select',
  },
];

export const LEAD_ENQUIRY_INFO = [
  {
    displayName: 'Enquired For',
    controlName: 'enquiredFor',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Property Type',
    controlName: 'basePropertyType',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Property Sub-Type',
    controlName: 'subPropertyType',
    placeholder: 'ex. select',
  },
  { displayName: 'BHK', controlName: 'noOfBHK', placeholder: 'ex. select' },
  {
    displayName: 'BHK Type',
    controlName: 'bhkType',
    placeholder: 'ex. select',
  },
  { displayName: 'Budget', controlName: 'budget', placeholder: 'ex. 10000' },
  {
    displayName: 'Enquired Location',
    controlName: 'enquiredLocation',
    placeholder: 'ex. HSR Layout',
  },
  {
    displayName: 'Carpet Area',
    controlName: 'carpetArea',
    placeholder: 'ex. 503',
  },
  {
    displayName: 'Build Up Area',
    controlName: 'buildUpArea',
    placeholder: 'ex. 145',
  },
  {
    displayName: 'Saleable Area',
    controlName: 'saleableArea',
    placeholder: 'ex. 145',
  },
  {
    displayName: 'Possession Needed By',
    controlName: 'possession',
    placeholder: 'ex. Human Resource',
  },
  {
    displayName: 'Preferred Floor',
    controlName: 'preferredFloor',
    placeholder: 'ex. 2',
  },
  {
    displayName: 'Associated Property',
    controlName: 'property',
    placeholder: 'ex. Utpal',
  },
  {
    displayName: 'Associated Project',
    controlName: 'project',
    placeholder: 'ex. Utpal',
  },
  {
    displayName: 'Agency Name',
    controlName: 'agencyName',
    placeholder: 'ex. Nowlak',
  },
  {
    displayName: 'Channel Partner Name',
    controlName: 'channelPartnerName',
    placeholder: 'ex. Deepak',
  },
  {
    displayName: 'Campaign Name',
    controlName: 'campaignName',
    placeholder: 'ex. Aamor',
  },
];

export const LEAD_ADDITIONAL_INFO = [
  { displayName: 'Gender', controlName: 'gender', placeholder: 'ex. select' },
  {
    displayName: 'Date of Birth',
    controlName: 'dateofBirth',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Father Name',
    controlName: 'fatherName',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Mother Name',
    controlName: 'motherName',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Religion',
    controlName: 'religion',
    placeholder: 'ex. select',
  },
  { displayName: 'Age', controlName: 'age', placeholder: 'ex. select' },
  {
    displayName: 'PAN Number',
    controlName: 'panNumber',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Aadhar Number',
    controlName: 'aadharNumber',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Voter ID',
    controlName: 'voterID',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Driving License',
    controlName: 'drivingLicense',
    placeholder: 'ex. select',
  },
  // { displayName: 'Referral Details', controlName: 'referralDetails', placeholder: 'ex. select' },
  {
    displayName: 'Profession',
    controlName: 'profession',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Customer Location',
    controlName: 'customerLocation',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Industry',
    controlName: 'industry',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Company Name',
    controlName: 'companyName',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Designation',
    controlName: 'designation',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Annual Income',
    controlName: 'annualIncome',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Work Location',
    controlName: 'workLocation',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Food preference',
    controlName: 'foodPreference',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Purpose of purchase',
    controlName: 'purposeofPurchase',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Security Deposit',
    controlName: 'securityDeposit',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Maintenance cost',
    controlName: 'maintenanceCost',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Down Payment',
    controlName: 'downPayment',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Mode of Payment',
    controlName: 'modeofPayment',
    placeholder: 'ex. select',
  },
  { displayName: 'Facing', controlName: 'facing', placeholder: 'ex. select' },
  {
    displayName: 'No. of Parking ',
    controlName: 'noofParking',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Current Address',
    controlName: 'currentAddress',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Current Address Type',
    controlName: 'currentAddressType',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Marital Status',
    controlName: 'maritalStatus',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Are you NRI',
    controlName: 'areYouNRI',
    placeholder: 'ex. select',
  },
];

export const LEAD_MARTIAL_STATUS = [
  {
    displayName: 'Spouse Name',
    controlName: 'spouseName',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Anniversary Date',
    controlName: 'anniversaryDate',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Primary Number',
    controlName: 'spousePhoneNo',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Alternative Number',
    controlName: 'spousealtPhoneNo',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Spousal DOB',
    controlName: 'spouseDOB',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Number of Children',
    controlName: 'noofChildren',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Spouse Profession',
    controlName: 'spouseProfession',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Company Name',
    controlName: 'spouseCompanyName',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Designation',
    controlName: 'spouseDesignation',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Annual Income',
    controlName: 'spouseAnnualIncome',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Work Location',
    controlName: 'spouseWorkLocation',
    placeholder: 'ex. select',
  },
];

export const LEAD_NRI = [
  { displayName: 'State', controlName: 'nriState', placeholder: 'ex. select' },
  { displayName: 'City', controlName: 'nriCity', placeholder: 'ex. select' },
  {
    displayName: 'Pin code',
    controlName: 'nriPinCode',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Locality',
    controlName: 'nriLocality',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Passport Type',
    controlName: 'passportType',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Visa Status',
    controlName: 'visaStatus',
    placeholder: 'ex. select',
  },
];

export const OTHERS = [
  {
    displayName: 'Sourcing Manager',
    controlName: 'sourcingManager',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Closing Manager',
    controlName: 'closingManager',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Channel Partner',
    controlName: 'channelPartner',
    placeholder: 'ex. select',
  },
  {
    displayName: 'Team Head',
    controlName: 'teamHead',
    placeholder: 'ex. select',
  },
  // { displayName: 'Customer Location', controlName: 'customerLocation', placeholder: 'ex. select' },
  // { displayName: 'Customer City', controlName: 'customerCity', placeholder: 'ex. select' },
  // { displayName: 'Customer State', controlName: 'customerState', placeholder: 'ex. select' },
];

export const BOOKING_FORM_FILE_UPLOAD_FIELDS = [
  {
    label: 'Upload Lead photo',
    labelText: 'click to Upload photo',
    controlName: 'leadPhoto',
    allImage: [] as any[],
    isUploading: false,
    errorMessage: 'Lead photo',
  },
  {
    label: 'Upload Aadhar',
    labelText: 'click to Upload Aadhar',
    controlName: 'aadharPhoto',
    allImage: [] as any[],
    isUploading: false,
    errorMessage: 'Aadhar',
  },
  {
    label: 'Upload PAN',
    labelText: 'click to Upload PAN',
    controlName: 'panPhoto',
    allImage: [] as any[],
    isUploading: false,
    errorMessage: 'PAN',
  },
  {
    label: 'Upload Passport',
    labelText: 'click to Upload Passport',
    controlName: 'passportPhoto',
    allImage: [] as any[],
    isUploading: false,
    errorMessage: 'Passport',
  },
];

export const PAYMENT_TYPE = [
  'Bank Loan',
  'Pending Loan Approval',
  'Partial Loan Cash',
  'Loan Applied',
  'Online Transfer',
  'Cash',
  'Cheque',
  'DD',
];

export const PAYMENT_MODES = [
  'Cheque',
  'DD',
  'IMPS',
  'NEFT',
  'UPI',
  'RTGS',
  'Cash',
];

export const PROJECT_STATUS = [
  'None',
  'Upcoming',
  'Ongoing',
  'Ready to move',
  'New',
  'Resale',
  'PreLaunch',
  'Launch',
];
export const PROJECT_CURRENT_STATUS = ['Available', 'Unavailable'];
export const PROJECT_NAV_STATUS = {
  new: 'New',
  pending: 'Pending',
  scheduled: 'Scheduled',
  overdue: 'Overdue',
  expressionOfInterestLeadCount: 'Expression Of Interest',
  booked: 'Booked',
  bookingCancel: 'Booking Cancel',
  'not-interested': 'Not Interested',
  dropped: 'Dropped',
  escalation: 'Escalated',
  highlighted: 'highlighted',
  aboutToconvert: 'about-to-convert',
  hot: 'hot',
  callback: 'callback',
  meetings: 'meetings',
  'site-visits': 'site-visits',
  'new-url': 'new',
};

export const FURNISH_STATUS = [
  { dispName: 'Unfurnished', value: 'Unfurnished' },
  { dispName: 'Furnished', value: 'Furnished' },
  { dispName: 'Semi-Furnished', value: 'Semifurnished' },
];

export const POSSESSION_DATE_FILTER_LIST = [
  { displayName: 'Immediate Possession (within 30 days)', value: 'Immediate Possession' },
  { displayName: 'Next 3 Months', value: 'Next 3 Months' },
  { displayName: '3-6 Months', value: '3-6 Months' },
  { displayName: '6-12 Months', value: '6-12 Months' },
  { displayName: 'More than 1 Year', value: 'More than 1 Year' },
  { displayName: 'Custom', value: 'Custom Date' },
];

export const PROJECT_FILTERS_KEY_LABEL: any = {
  ProjectStatuses: 'Status',
  currentStatus: 'Availability',
  MinBudget: 'Min Price',
  MaxBudget: 'Max Price',
  Possesion: 'Possession Date',
  FromPossesionDate: 'Possession From Date',
  ToPossesionDate: 'Possession To Date',
  FromDate: 'Start Date',
  ToDate: 'End Date',
  BuilderName: 'Builder Name',
  projectSubType: 'Project SubType',
  carpetArea: 'Carpet Area',
  AmenitesIds: 'Amenities',
  Locations: 'Location',
  facings: 'Facing',
  MinLeadCount: 'Min Lead',
  MaxLeadCount: 'Max Lead',
  MinProspectCount: 'Min Data',
  MaxProspectCount: 'Max Data',
  MinCarpetArea: 'Min Land Area',
  MaxCarpetArea: 'Max Land Area',
};

export const PROJECT_APPLIED_FILTER_KEYS: any = {
  currentStatus: 'projectCurrentStatus',
  projectStatus: 'ProjectStatuses',
  ToDate: 'endDate',
  FromDate: 'startDate',
  Possesion: 'possessionDate',
  MinPrice: 'MinBudget',
  MaxPrice: 'MaxBudget',
  Locations: 'locations',
  AmenitesIds: 'amenities',
};

export const MANAGE_USER_FILTERS_KEY_LABEL: any = {
  users: 'User',
  reportsTo: 'Reporting To',
  department: 'Department',
  designation: 'Designation',
  generalManager: 'General Manager',
  DateType: 'Date Type',
  date: 'Date',
  timezone: 'TimeZone',
};

export const ATTENDANCE_FILTERS_KEY_LABEL: any = {
  users: 'User',
  reportsTo: 'Reporting To',
  department: 'Department',
  designation: 'Designation',
};

export const TO_PERFORMER_FILTERS_KEY_LABEL: any = {
  Projects: 'Project',
  Sources: 'Source',
  SubSources: 'SubSource',
};

export const DIRECTION_OF_LEAD_CREATION = [
  { displayName: 'Lead', value: 0 },
  { displayName: 'Data', value: 1 },
  { displayName: 'Both', value: 2 },
];

export const OFFERING_TYPE = [
  { displayName: 'Ready', value: 1 },
  { displayName: 'Off Plan', value: 2 },
  { displayName: 'Secondary', value: 3 },
];

export const DAYS = [
  {
    value: 0,
    id: 'sunday',
    name: 'Sunday',
    shortName: 'Sun',
    shiftFrom: 'sundayFrom',
    shiftTo: 'sundayTo',
  },
  {
    value: 1,
    id: 'monday',
    name: 'Monday',
    shortName: 'Mon',
    shiftFrom: 'mondayFrom',
    shiftTo: 'mondayTo',
  },
  {
    value: 2,
    id: 'tuesday',
    name: 'Tuesday',
    shortName: 'Tue',
    shiftFrom: 'tuesdayFrom',
    shiftTo: 'tuesdayTo',
  },
  {
    value: 3,
    id: 'wednesday',
    name: 'Wednesday',
    shortName: 'Wed',
    shiftFrom: 'wednesdayFrom',
    shiftTo: 'wednesdayTo',
  },
  {
    value: 4,
    id: 'thursday',
    name: 'Thursday',
    shortName: 'Thu',
    shiftFrom: 'thursdayFrom',
    shiftTo: 'thursdayTo',
  },
  {
    value: 5,
    id: 'friday',
    name: 'Friday',
    shortName: 'Fri',
    shiftFrom: 'fridayFrom',
    shiftTo: 'fridayTo',
  },
  {
    value: 6,
    id: 'saturday',
    name: 'Saturday',
    shortName: 'Sat',
    shiftFrom: 'saturdayFrom',
    shiftTo: 'saturdayTo',
  },
];

export const UNIT_INFO_FILTERS_KEY_LABEL: any = {
  Area: 'Unit Area',
  userSearch: 'Unit Name',
  CarpetArea: 'Carpet Area',
  BuiltupArea: 'Builtup Area',
  SuperBuiltupArea: 'Super Builtup Area',
  MaintenanceCost: 'Maintenance Cost',
  PricePerUnit: 'Price per unit',
  TotalPrice: 'Total Price',
  UnitType: 'Unit Type',
  UnitSubType: 'Unit sub Type',
  BHKs: 'BHKs',
  BHKTypes: 'BHK Type',
  Facings: 'Facing',
  FurnishingStatuses: 'Furnishing Status',
  NoOfBalconies: 'Balconies',
  NoOfBathrooms: 'Bath Rooms',
  NoOfLivingrooms: 'Drawing or Living Rooms',
  NoOfBedrooms: 'Bed Rooms',
  NoOfUtilites: 'Utilities',
  NoOfKitchens: 'Kitchens',
  NoOfMaximumOccupants: 'Maximum Occupants',
  MinArea: 'Min Unit Area',
  MaxArea: 'Max Unit Area',
};

export const WATR_MARK_POSITIONS = [
  {
    position: 'Center',
  },
  {
    position: 'Left Top',
  },
  {
    position: 'Right Top',
  },
  {
    position: 'Left Bottom',
  },
  {
    position: 'Right Bottom',
  },
  {
    position: 'Left Center',
  },
  {
    position: 'Right Center',
  },
  {
    position: 'Top Center',
  },
  {
    position: 'Bottom Center',
  },
];

export const moduleSettings: ModuleSettings[] = [
  {
    name: 'general',
    label: 'SIDEBAR.general',
    description: 'Common platform configuration',
    iconColor: 'bg-green-900',
    iconClass: 'ic-hexagon',
    path: 'global-config/general-settings',
  },
  {
    name: 'security',
    label: 'Security',
    description: '2FA security settings can be done',
    iconColor: 'bg-orange-800',
    iconClass: 'ic-guard',
    path: 'global-config/security-settings',
  },
  {
    name: 'attendance',
    label: 'SIDEBAR.module-attendance',
    description: 'Manage your shift timings',
    iconColor: 'bg-blue-350',
    iconClass: 'ic-completed',
    path: 'global-config/attendance-settings',
  },
  {
    name: 'leads',
    label: 'SIDEBAR.leads',
    description: 'Customized lead features',
    iconColor: 'bg-accent-green',
    iconClass: 'ic-secondary-filter-solid',
    path: 'global-config/lead-settings',
  },
  {
    name: 'data',
    label: 'CONTACT.data',
    description: 'Customized data features',
    iconColor: 'bg-brown-200',
    iconClass: 'ic-address-card-solid',
    path: 'global-config/data-settings',
  },
  {
    name: 'projects and properties',
    label: 'Projects and Properties',
    description: 'Customized property & project features',
    iconColor: 'bg-blue-150',
    iconClass: 'ic-house-solid',
    path: 'global-config/property-project-settings',
  },
  {
    name: 'locality',
    label: 'LOCATION.locality',
    description: 'Customized locality feature',
    iconColor: 'bg-red-450',
    iconClass: 'ic-location-circle',
    path: 'global-config/locality-settings',
  },
  {
    name: 'qr',
    label: 'QR Code',
    description: 'Customize the QR enquiry form',
    iconColor: 'bg-navy-200',
    iconClass: 'ic-qr-code',
    path: 'global-config/manage-qr',
  },
  {
    name: 'tags',
    label: 'Tags',
    description: 'Customized tags',
    iconColor: 'bg-green-190',
    iconClass: 'ic-tags',
    path: 'global-config/custom-tags',
  },
  {
    name: 'manage-marketing',
    label: 'Manage Marketing',
    description: 'Customized marketing efforts',
    iconColor: 'bg-dark-green-100',
    iconClass: 'ic-social-profile',
    path: 'global-config/marketing',
  },
  {
    name: 'data-migration',
    label: 'Data Migration',
    description: 'Migrate Leads/Data',
    iconColor: 'bg-orange-550',
    iconClass: 'ic-folder-transfer',
    path: 'global-config/migration',
  },
  // {
  //   name: 'manage-form',
  //   label: 'Manage Form',
  //   description: 'Customized CRM forms',
  //   iconColor: 'bg-orange-550',
  //   iconClass: 'ic-folder-transfer',
  //   path: 'global-config/form-settings',
  // },
  {
    name: 'listing-management',
    label: 'Listing Management',
    description: 'Customized listing',
    iconColor: 'bg-violet-800',
    iconClass: 'ic-converter',
    path: 'global-config/listing-management',
  },
  {
    name: 'Automation',
    label: 'Automation',
    description: 'Automate the setting',
    iconColor: 'bg-blue-1150',
    iconClass: 'ic-setting-connections',
    path: 'global-config/report-automation'
  },
  {
    name: 'manage-source',
    label: 'Manage Source',
    description: 'Manage Source',
    iconColor: 'bg-navy-300',
    iconClass: 'ic-circle-nodes',
    path: 'global-config/manage-source',
  }
];

export const communicationSettings: ModuleSettings[] = [
  {
    name: 'templates',
    label: 'Templates',
    description: 'Add / Edit Templates',
    iconColor: 'bg-brown-150',
    iconClass: 'ic-book',
    path: 'global-config/templates',
  },
  {
    name: 'ivr',
    label: 'Ivr',
    description: 'IVR Caller System Setup',
    iconColor: 'bg-blue-750',
    iconClass: 'ic-ivr',
    path: 'global-config/templates',
  },
  {
    name: 'email',
    label: 'Email',
    description: 'Email Configuration',
    iconColor: 'bg-blue-550',
    iconClass: 'ic-envelope-solid',
    path: 'global-config/email-settings',
  },
];

export const SOURCE_IMAGE = [
  {
    leadSource: 0,
    imageURL: 'logos/direct.svg',
  },
  {
    leadSource: 1,
    imageURL: 'logos/IVR.svg',
  },
  {
    leadSource: 2,
    imageURL: 'logos/facebook.svg',
  },
  {
    leadSource: 3,
    imageURL: 'logos/linkedin.svg',
  },
  {
    leadSource: 4,
    imageURL: 'logos/google_ads.svg',
  },
  {
    leadSource: 5,
    imageURL: 'logos/magic_bricks.svg',
  },
  {
    leadSource: 6,
    imageURL: 'logos/ninetynine_acres.svg',
  },
  {
    leadSource: 7,
    imageURL: 'logos/housing.svg',
  },
  {
    leadSource: 8,
    imageURL: 'logos/Gharoffice.svg',
  },
  {
    leadSource: 9,
    imageURL: 'logos/refferal.svg',
  },
  {
    leadSource: 10,
    imageURL: 'logos/direct.svg',
  },
  {
    leadSource: 11,
    imageURL: 'logos/website.svg',
  },
  {
    leadSource: 12,
    imageURL: 'logos/gmail.svg',
  },
  {
    leadSource: 13,
    imageURL: 'logos/microsite.svg',
  },
  {
    leadSource: 14,
    imageURL: 'logos/Portfolio.svg',
  },
  {
    leadSource: 15,
    imageURL: 'logos/phonebook.svg',
  },
  {
    leadSource: 16,
    imageURL: 'logos/call_logs.svg',
  },
  {
    leadSource: 17,
    imageURL: 'logos/Leadpool.svg',
  },
  {
    leadSource: 18,
    imageURL: 'logos/square_yards.svg',
  },
  {
    leadSource: 19,
    imageURL: 'logos/quikr_homes.svg',
  },
  {
    leadSource: 20,
    imageURL: 'logos/just_lead.svg',
  },
  {
    leadSource: 21,
    imageURL: 'logos/whatsapp.svg',
  },
  {
    leadSource: 22,
    imageURL: 'logos/youtube.svg',
  },
  {
    leadSource: 23,
    imageURL: 'logos/qr_code.svg',
  },
  {
    leadSource: 24,
    imageURL: 'logos/instagram.svg',
  },
  {
    leadSource: 25,
    imageURL: 'logos/olx.svg',
  },
  {
    leadSource: 26,
    imageURL: 'logos/estate_dekho.svg',
  },
  {
    leadSource: 27,
    imageURL: 'logos/google_sheets.svg',
  },
  {
    leadSource: 28,
    imageURL: 'logos/channel_partner.svg',
  },
  {
    leadSource: 29,
    imageURL: 'logos/real_estateIndia.svg',
  },
  {
    leadSource: 30,
    imageURL: 'logos/common_floors.svg',
  },
  {
    leadSource: 31,
    imageURL: 'logos/data_management.svg',
  },
  {
    leadSource: 32,
    imageURL: 'logos/roof_and_floor.svg',
  },
  {
    leadSource: 33,
    imageURL: 'logos/microsoft_ads.svg',
  },
  {
    leadSource: 34,
    imageURL: 'logos/propertywalaboxlogo.svg',
  },
  {
    leadSource: 35,
    imageURL: 'logos/project_microsite.svg',
  },
  {
    leadSource: 36,
    imageURL: 'logos/MyGate.svg',
  },
  {
    leadSource: 37,
    imageURL: 'logos/flipkart.svg',
  },
  {
    leadSource: 38,
    imageURL: 'logos/PropertyFinderNew.svg',
  },
  {
    leadSource: 39,
    imageURL: 'logos/Bayut.svg',
  },
  {
    leadSource: 40,
    imageURL: 'logos/Dubizzle.svg',
  },
  {
    leadSource: 41,
    imageURL: 'logos/webhook-logo.svg',
  },
  {
    leadSource: 42,
    imageURL: 'logos/TikTok.svg',
  },
  {
    leadSource: 43,
    imageURL: 'logos/Snapchat.svg',
  },
];

export const DELETED_NOTES = [
  'All assigned leads, data, and secondary leads of the user will be unassigned.',
  'All the deleted leads, data, and secondary leads associated with the user will be removed.',
  'All locality, IVR, city, and zone assignments to the user will be removed and unassigned.',
  'All property and project assignments to the user will be removed and unassigned.',
  'All the deleted properties and project assignments associated with the user will be removed.',
  'All integration accounts and Facebook account assignments to the user will be removed.',
];

export const ASSIGNED_FIELDS = [
  {
    key: 'leads',
    label: 'Leads',
  },
  {
    key: 'secondoryLeads',
    label: 'Secondary Owner Leads',
  },
  {
    key: 'deletedLeads',
    label: 'Deleted Leads',
  },
  {
    key: 'secDeletedLeads',
    label: 'Secondary Owner Deleted Leads',
  },
  {
    key: 'projects',
    label: 'Projects',
  },
  {
    key: 'deletedProjects',
    label: 'Deleted Projects',
  },
  {
    key: 'facebookAccountInfo',
    label: 'Facebook accounts',
  },
  {
    key: 'ivrAssignment',
    label: 'IVR assignment',
  },
  {
    key: 'localityAssignment',
    label: 'Locality assignment',
  },
  {
    key: 'prospects',
    label: 'Data',
  },
  {
    key: 'deletedProspects',
    label: 'Deleted Data',
  },
  {
    key: 'property',
    label: 'Properties',
  },
  {
    key: 'deletedProperty',
    label: 'Deleted Properties',
  },
  {
    key: 'integrationAccountInfo',
    label: 'Integration accounts',
  },
  {
    key: 'cityAssignment',
    label: 'City assignment',
  },
  {
    key: 'zoneAssignment',
    label: 'Zone assignment',
  },
  {
    key: 'qrTemplateAssignment',
    label: 'QR template assignment',
  },
];

export const REQUEST_TYPES = ['Whatsapp', 'IVR', 'Email'];

export const PF_REQUEST_TYPES = ['Whatsapp', 'Call', 'Email'];

export const REQUEST_TYPE_MAPPING: { [key: string]: string } = {
  whatsapp_leads: 'Whatsapp',
  call_logs: 'IVR',
  leads: 'Email',
};

export const MODULE_INFO: { [key: string]: { [key: string]: string } } = {
  'Manage Leads': {
    'Lead Module':
      'The Lead Module in CRM allows users to add, track, and manage leads efficiently by offering features like manual and bulk lead addition, detailed lead tracking, and various actions like editing, deleting, and communicating with leads. It supports advanced data management through import/export functionalities and customizable views, enabling users to filter and update lead information. Additionally, it maintains lead history integrity while providing privacy controls during lead reassignment.',
    'Add Lead': 'Helps to add leads manually.',
    'Bulk Upload': 'Helps to add leads in bulk via a template.',
    'QR Generator':
      'Assists in adding leads by scanning a QR code and filling out the lead form.',
    'Tracker (ExportTracker /Bulk Upload Tracker)':
      'Used to track the status of bulk upload and export of leads.',
    'Manage Columns':
      'Allows users to add additional columns to the main lead table for viewing more detailed information.',
    'Advanced Filter':
      'The purpose of filter is to help users narrow down and view specific leads based on chosen criteria, such as date, status, property, project, lead source, and other relevant fields, making it easier to analyze and manage leads effectively.',
    'Refresh button': 'Used to refresh the data.',
    'Calender Icon': 'Is the Date filter to select date.',
    All: 'Represents the total number of leads in the CRM.',
    'My leads': 'Represent the total number of leads of the agent/user.',
    "Team's": 'Represents the total number of leads of the team.',
    Unassigned: 'Represents the total number of unassigned leads in the CRM.',
    Deleted: 'Represents the total number of deleted leads from CRM',
    Duplicate:
      'Represents the total number of duplicate leads, on clicking user can see the duplicate leads in the CRM.',
    'Active leads':
      'Represents the active leads that are neither dropped nor marked as not interested in the CRM.',
    New: 'Represents new leads',
    Pending:
      'represents new leads with no activity, such as calls, SMS, WhatsApp messages, or status updates, initiated.',
    Scheduled:
      'represents the leads for which meeting, site visit and callbacks are scheduled at a certain data and time.',
    Overdue:
      'represents leads that were missed for follow-up because the tasks on them werent updated.',
    'Expression of Interest':
      'represents the leads that are not interested right now but maybe interested in future, or want callback in future.',
    'Booking cancel':
      'represents leads for which the booking has been canceled.',
    Invoiced: 'represents leads for which an invoice has been issued.',
    Dropped:
      'represents leads that are disqualified, or not pursuing further due to lack of engagement, budget constraints, or other reasons.',
    'Not interested':
      'represents the leads that are  not interested in the product or service and will not continue with further communication.',
    Booked:
      'represents leads that showed interest & commitment, and were booked after paying the token or full amount',
    Actions:
      'Used to perform actions on lead, such as editing, deleting, adding notes, viewing lead history and documents, sending emails or WhatsApp messages, calling the lead, and accessing call recordings of conversations with the lead.',
    Export: 'Used to export the lead information in an excel format.',
    Default:
      'It is used to reset the lead table to its default view, removing added columns.',
    Source: 'Indicates the source from which the lead originated.',
    'Bulk Source': 'helps to update the source of the selected leads.',
    'Bulk Project': 'helps to update projects of the selected leads.',
    'Bulk WhatsApp':
      'allows to bulk share message and details to the selected leads via WhatsApp. ( Post WhatsApp business integration ) otherwise only one to one WhatsApp texts.',
    'Bulk Email':
      'allows to send bulk email to the selected leads. ( only happens post smtp integration , also email has to be mentioned in clients lead form.',
    'Bulk delete': 'allows to bulk delete the selected leads.',
    'Chat symbol': 'allows to call/WhatsApp customer support for more help.',
  },
  Invoice: {
    'Invoice Module':
      'The invoice module in CRM allows users to manage and track invoices for leads. This module ensures efficient invoicing and provides detailed reports for better financial oversight. User can filter out the invoiced leads based on city, locality, enquired project, enquiry type, tags, agency name, sub source and so on.',
    All: 'Represents the total number of leads invoiced in the CRM, where the main booking form has been completed.',
    'My leads':
      'Represents the total number of leads invoiced by the agent/user.',
    Teams:
      'Represents the total number of leads invoiced by the users team (where the user is the team leader or manager).',
    Deleted:
      'Represents the total number of invoiced leads located in the delete bucket of the CRM that have not been permanently removed; the user can still view these leads.',
    Duplicate:
      'Represents the number of invoiced duplicate leads; clicking on it allows the user to view the invoiced duplicate leads in the CRM.',
    Invoiced:
      'Sub heading just reflects the number when above headings/categories are selected.',
    'Lead Name':
      'Represents the name of the invoiced leads, user can click on "Lead Name" and get the lead overview including lead enquiry info, phone number, email, budget and so on.',
    'Assigned To': 'Displays the primary and secondary owners of the lead.',
    Status: 'Displays the status as "Invoiced" for the lead.',
    'Agency Name': 'Represents the Agency associated with the Lead.',
    Source: 'Indicates the source from which the lead originated.',
    Actions:
      'Used to perform actions on lead, such as editing, deleting, adding notes, viewing lead history and documents, sending emails or WhatsApp messages, calling the lead, and accessing call recordings of conversations with the lead.',
    'Search bar': 'to search a lead by name, source.',
    Export: 'Used to export the lead information in an excel format.',
    'Manage Columns':
      'Allows users to add additional columns for additional info such as "Add on charges", "Agency Name", "Agreement Value", "Balance Amount", "Book Under Name", "Brokerage Charge", "Brokerage Earned", "Token Amount Paid" and so on to the main lead table for viewing more detailed information.',
    Default:
      'It is used to reset the lead table to its default view, removing added columns.',
    'Refresh button': 'used to refresh the data.',
    'Checkbox next to Lead Name': 'Allows to bulk select the leads.',
    'Calendar Icon': 'is the Date filter to select date.',
    'Tracker (Export Tracker/Bulk Operation Tracker)':
      'Used to track the status of bulk exports and bulk operations performed on the leads like bulk email, bulk WhatsApp, bulk delete, bulk status update and so on.',
    'Advanced Filter':
      'The purpose of filter is to help users narrow down and view specific leads based on chosen criteria, such as date, status, property, project, lead source, and other relevant fields, making it easier to analyze and manage leads effectively.',
    'Bulk Update Status': 'allows to update the status of bulk selected leads.',
    'Reassign Leads':
      'helps re-assigning the selected leads to another primary owner. ( with history , without history and new status or as it is .)',
    'Secondary Reassign Leads':
      'helps re-assign the selected lead to another secondary owners. ( provided two people will work simultaneously on a lead )',
    'Bulk Source': 'helps to update the source of the selected leads.',
    'Bulk Project': 'helps to update projects of the selected leads.',
    'Bulk WhatsApp':
      'allows to bulk share message and details to the selected leads via WhatsApp. ( Post WhatsApp business integration ) otherwise only one to one WhatsApp texts.',
    'Bulk Email':
      'allows to send bulk email to the selected leads. ( only happens post smtp integration , also email has to be mentioned in clients lead form.',
    'Bulk delete': 'allows to bulk delete the selected leads.',
    'Chat symbol': 'allows to call/WhatsApp customer support for more help.',
  },
};

export const WA_BUTTON_TYPE = [
  { type: 'None', icon: 'ic-turn-right' },
  { type: 'QuickReply', icon: 'ic-curve-arrow' },
  { type: 'PHONE_NUMBER', icon: 'ic-Call' },
  { type: 'URL', icon: 'ic-link' },
  { type: 'COPY_CODE', icon: 'ic-copy-clipboard' },
];

export const securityDepositDates: { label: string; value: number }[] = [
  { label: '3 Months Rent', value: 1 },
  { label: '6 Months Rent', value: 2 },
  { label: '10 Months Rent', value: 3 },
  { label: '1 Year Rent', value: 4 },
];
export const lockInPeriodList: { label: string; value: number }[] = [
  { label: '1 year', value: 1 },
  { label: '2 years', value: 2 },
  { label: '3 years', value: 3 },
  { label: '4 years', value: 4 },
  { label: '5 years', value: 5 },
  { label: '5+ years', value: 6 },
];

export const noticePeriodList: { label: string; value: number }[] = [
  { label: '30 Days', value: 1 },
  { label: '45 Days', value: 2 },
  { label: '60 Days', value: 3 },
  { label: '90 Days', value: 4 },
  { label: '100 Days', value: 5 },
];

export const LISTING_FIRSTLEVELFILTER = [
  {
    enumValue: 0,
    name: 'All',
    displayName: 'All',
  },
  {
    enumValue: 1,
    name: 'Ready',
    displayName: 'Ready',
  },
  {
    enumValue: 2,
    name: 'OffPlan',
    displayName: 'Off Plan',
  },
  {
    enumValue: 3,
    name: 'Secondary',
    displayName: 'Secondary',
  },
];

export const OFFERINGTYPE = [
  {
    enumValue: 1,
    name: 'Ready',
    displayName: 'Ready',
  },
  {
    enumValue: 2,
    name: 'OffPlan',
    displayName: 'Off Plan',
  },
  {
    enumValue: 3,
    name: 'Secondary',
    displayName: 'Secondary',
  },
];

export const COMPLETIONSTATUS = [
  {
    enumValue: 1,
    name: 'Completed',
    displayName: 'Completed',
  },
  {
    enumValue: 2,
    name: 'OffPlan',
    displayName: 'Off Plan',
  },
  {
    enumValue: 3,
    name: 'CompletedPrimary',
    displayName: 'Completed-Primary',
  },
  {
    enumValue: 4,
    name: 'OffPlanPrimary',
    displayName: 'Off Plan-Primary',
  },
];

export const LEADS_FILTERS_V2 = [
  { displayName: 'All Leads', value: 0, status: 'all_leads' },
  { displayName: 'Active Leads', value: 1, status: 'active_leads' },
  { displayName: 'Not Interested', value: 2, status: 'not_interested' },
  { displayName: 'Dropped', value: 3, status: 'dropped' },
  { displayName: 'Booked', value: 4, status: 'booked' },
  { displayName: 'Booking Cancel', value: 5, status: 'booking_cancel' },
  { displayName: 'Invoiced', value: 6, status: 'invoiced' },
];

export const LEADS_VISIBILITY_V2 = [
  { displayName: 'All', value: 0 },
  { displayName: 'My Leads', value: 1 },
  { displayName: 'Teams', value: 2 },
  { displayName: 'Unassigned', value: 3 },
  { displayName: 'Deleted', value: 4 },
  { displayName: 'Duplicate', value: 5 },
];

export const PURPOSE_LIST = [
  { displayName: 'Investment', value: 1 },
  { displayName: 'Self Use', value: 2 },
];

export const ATTR_NO = ['1', '2', '3', '4', '5+'];

export const ATTR_NO_ALL = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
];

export const UAE_EMIRATES = [
  {
    value: 1,
    name: 'dubai',
    displayName: 'Dubai',
  },
  {
    value: 2,
    name: 'abu_dhabi',
    displayName: 'Abu Dhabi',
  },
  {
    value: 3,
    name: 'northern_emirates',
    displayName: 'Northern Emirates',
  },
];

export const FINISHINGTYPE = [
  {
    value: 1,
    name: 'FullyFinished',
    displayName: 'Fully-Finished',
  },
  {
    value: 2,
    name: 'SemiFinished',
    displayName: 'Semi-Finished',
  },
  {
    value: 3,
    name: 'Unfinished',
    displayName: 'Unfinished',
  },
]

export const CALL_DIRECTIONS = [
  { displayName: 'Incoming', value: 1 },
  { displayName: 'Outgoing', value: 2 }
];

export const CALL_STATUSES = [
  { displayName: 'Missed', value: 1 },
  { displayName: 'Disconnected', value: 2 },
  { displayName: 'Answered', value: 3 },
];

export const LEAD_FILTER_TYPES = [
  { displayName: 'Show Unique', value: 0 },
  { displayName: 'Show Parent', value: 1 },
  { displayName: 'Show Duplicate', value: 2 }
];

export const MARITAL_STATUS = [{ name: 'Single', value: 1 }, { name: 'Married', value: 2 }]

export const LISTING_POSSESSION_TYPE = [
  { displayName: 'Custom Date', value: 'Custom Date' },
  { displayName: 'Immediate', value: 'Immediate' }
];

export const EVENT_TYPE = ['Lead Created', 'Lead Updated']

export const INDEXEDDB_STORES = [
  'customTagsData',
  'propertyListStore',
  'projectListStore',
  'areaUnitData',
];
