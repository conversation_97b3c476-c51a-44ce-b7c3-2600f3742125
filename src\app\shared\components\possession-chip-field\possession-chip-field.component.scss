.possession-radio-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.possession-radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.possession-radio-input {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
  accent-color: #2c2c2c;
}

.possession-radio-label {
  font-size: 14px;
  font-weight: 400;
  color: #374151;
  cursor: pointer;
  user-select: none;
  margin: 0;
  line-height: 1.4;
}

.possession-radio-input:checked + .possession-radio-label {
  color: #2c2c2c;
  font-weight: 500;
}

.custom-date-section {
  padding-left: 24px; // Align with radio button labels

  .field-rupees-tag {
    max-width: 250px;
  }
}

// Focus styles for accessibility
.possession-radio-input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

// Disabled state
.possession-radio-item.disabled {
  pointer-events: none;
  opacity: 0.6;

  .possession-radio-input,
  .possession-radio-label {
    cursor: not-allowed;
  }
}

// Responsive design
@media (max-width: 768px) {
  .possession-radio-container {
    gap: 6px;
  }

  .possession-radio-label {
    font-size: 13px;
  }
}
