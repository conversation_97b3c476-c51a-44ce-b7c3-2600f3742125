<div class="flex-column">
  <div class="field-label" *ngIf="showLabel">{{ label || 'Possession Needed By' }}</div>
  
  <!-- Chip Layout for Possession Options -->
  <div class="possession-chips-container d-flex flex-wrap">
    <ng-container *ngFor="let option of possessionOptions; let i = index">
      <div class="possession-chip mr-8 mb-8" 
           [ngClass]="{'selected': selectedPossession === option.value}"
           (click)="selectPossession(option.value)">
        <input type="radio" 
               [id]="'possession-' + i" 
               [name]="radioGroupName"
               [(ngModel)]="selectedPossession"
               [value]="option.value" 
               [ngModelOptions]="{standalone: true}" 
               class="possession-radio">
        <label [for]="'possession-' + i" class="possession-label">
          {{ option.displayName }}
        </label>
      </div>
    </ng-container>
  </div>

  <!-- Custom Date Section -->
  <div *ngIf="selectedPossession === 'Custom Date'" class="custom-date-section mt-12">
    <div class="text-dark-gray text-large mb-8">Select Possession Date</div>
    <div class="position-relative field-rupees-tag" style="max-width: 250px;">
      <div class="rupees-sm icon ic-calendar ic-xxs ic-coal"></div>
      <input type="text" 
             readonly 
             [owlDateTimeTrigger]="dtCustomPossession" 
             [owlDateTime]="dtCustomPossession"
             class="h-32"
             placeholder="dd-mm-yyyy"
             [value]="getFormattedCustomDate()"
             (ngModelChange)="onCustomDateChange($event)" 
             [ngModelOptions]="{standalone: true}"
             [ngModel]="customDate" />
      <owl-date-time [pickerType]="'calendar'" 
                     #dtCustomPossession 
                     [startAt]="minDate"
                     [min]="minDate">
      </owl-date-time>
    </div>
    
    <!-- Date Validation Error -->
    <div *ngIf="showDateValidationError" class="text-xs text-red fw-semi-bold mt-6">
      Please select a valid future date
    </div>
  </div>
</div>
