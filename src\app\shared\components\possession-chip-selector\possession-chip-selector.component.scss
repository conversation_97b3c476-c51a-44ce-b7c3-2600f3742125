.possession-chips-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.possession-chip {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &.selected {
    .possession-label {
      background-color: #2c2c2c;
      color: white;
      border-color: #2c2c2c;
    }
  }
}

.possession-radio {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.possession-label {
  display: inline-block;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  user-select: none;

  &:hover {
    border-color: #9ca3af;
    background-color: #f9fafb;
  }
}

.custom-date-section {
  .date-picker-container {
    max-width: 200px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .possession-chips-container {
    gap: 6px;
  }
  
  .possession-label {
    padding: 6px 12px;
    font-size: 13px;
  }
}

// Focus styles for accessibility
.possession-radio:focus + .possession-label {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

// Disabled state
.possession-chip.disabled {
  pointer-events: none;
  opacity: 0.6;
  
  .possession-label {
    cursor: not-allowed;
  }
}
