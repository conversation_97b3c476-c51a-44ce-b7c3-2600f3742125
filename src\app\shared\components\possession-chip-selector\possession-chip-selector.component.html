<div class="flex-column">
  <div class="field-label" *ngIf="showLabel">{{ label || 'Possession Needed By' }}</div>
  
  <!-- Chip Layout for Possession Options -->
  <div class="possession-chips-container">
    <ng-container *ngFor="let option of dateFilterList; let i = index">
      <div class="possession-chip" 
           [ngClass]="{'selected': selectedPossession === option.value}"
           (click)="selectPossession(option.value)">
        <input type="radio" 
               [id]="'possession-' + i" 
               [name]="radioGroupName"
               [value]="option.value"
               [(ngModel)]="selectedPossession"
               [ngModelOptions]="{standalone: true}"
               class="possession-radio">
        <label [for]="'possession-' + i" class="possession-label">
          {{ option.displayName }}
        </label>
      </div>
    </ng-container>
  </div>

  <!-- Custom Date Picker -->
  <div *ngIf="selectedPossession === 'Custom Date'" class="custom-date-section mt-12">
    <div class="field-label">Select Date</div>
    <div class="date-picker-container">
      <div class="position-relative field-rupees-tag">
        <div class="rupees-sm icon ic-calendar ic-xxs ic-coal"></div>
        <input type="text" 
               readonly 
               [owlDateTimeTrigger]="dtCustomPossession" 
               [owlDateTime]="dtCustomPossession"
               class="h-32"
               placeholder="dd-mm-yyyy"
               [value]="getFormattedDate()"
               (ngModelChange)="onCustomDateChange($event)" 
               [ngModelOptions]="{standalone: true}"
               [ngModel]="customDate" />
        <owl-date-time [pickerType]="'calendar'" 
                       #dtCustomPossession 
                       [startAt]="minDate"
                       (afterPickerClosed)="onDatePickerClosed()">
        </owl-date-time>
      </div>
    </div>
    
    <!-- Date Validation Error -->
    <div *ngIf="showDateValidationError" class="text-xs text-red fw-semi-bold mt-6">
      Please select a valid future date
    </div>
  </div>
</div>
