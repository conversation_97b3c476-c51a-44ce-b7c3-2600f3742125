import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { countries } from 'countries-list';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs';

import {
  APP_VERSION,
  BHK_NO,
  BHK_NO_ALL,
  BHK_TYPE,
  FURNISH_STATUS,
  GENDER,
  MARITAL_STATUS,
  MONTHS,
  NUMBER_10,
  OFFERING_TYPE,
  POSSESSION_DATE_FILTER_LIST,
  PROPERTY_LIST,
  PROPERTY_TYPE_LIST,
  PURPOSE_LIST,
  QR_DISPLAY_FIELDS,
  TRANSACTION_TYPE_LIST,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import {
  BHKType,
  EnquiryType,
  FurnishStatus,
  PossessionType,
  Profession,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Profile } from 'src/app/core/interfaces/profile.interface';
import {
  changeCalendar,
  formatBudget,
  generateFloorOptions,
  getBHKDisplayString,
  getLocationDetailsByObj,
  getPropertyTypeIds,
  getTimeZoneDate,
  getTenantName,
  onlyNumbers,
  onPickerOpened,
  setPropertySubTypeList,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields,
  allowLandlineInput,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { FetchGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { AddQRLead, FetchAgencyNameListAnonymous, FetchCampaignListAnonymous, FetchChannelPartnerListAnonymous } from 'src/app/reducers/lead/lead.actions';
import { getCampaignListAnonymous, getAgencyNameListAnonymous, getChannelPartnerListAnonymous } from 'src/app/reducers/lead/lead.reducer';
import {
  FetchQRAreaUnitList,
  FetchQRPropertyTypesList,
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getQrAreaUnits,
  getQrPropertyTypes,
} from 'src/app/reducers/master-data/master-data.reducer';
import {
  FetchAssignedProjectsList,
  FetchProjectIdWithName,
} from 'src/app/reducers/project/project.action';
import {
  getAssignedProjects,
  getProjectsIDWithName,
} from 'src/app/reducers/project/project.reducer';
import { FetchQrFormById } from 'src/app/reducers/qr-form/qr-form.action';
import { getQRFormById } from 'src/app/reducers/qr-form/qr-form.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'lead-qr-form',
  templateUrl: './lead-qr-form.component.html',
})
export class LeadQRFormComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  addLeadForm: FormGroup;
  placesList: any[] = [];
  propSubTypes: Array<{ displayName: string }> = [];
  bhkTypeList: Array<string> = [];
  propTypeImg: {} = PROPERTY_TYPE_LIST;
  bhkTypes: Array<string> = BHK_TYPE;
  bhkNoList: Array<string> = BHK_NO;
  noOfBhk: Array<string> = BHK_NO_ALL.map((bhk: any) =>
    getBHKDisplayString(bhk)
  );
  enquiredForList: Array<any> = TRANSACTION_TYPE_LIST;
  purposeList: Array<any> = PURPOSE_LIST;
  PropertyType: Array<string> = PROPERTY_LIST;
  preferredCountries = ['in'];
  duplicateLead: any;
  projectList: Array<string> = [];
  propertyList: Array<string> = [];
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  getLocationDetailsByObj = getLocationDetailsByObj;
  getBHKDisplayString = getBHKDisplayString;
  getTimeZoneDate = getTimeZoneDate;
  lowerBudgetInWords: string = '';
  upperBudgetInWords: string = '';
  defaultCurrency: string = '';
  isShowReferralFields: boolean = false;
  resetNumber: boolean = false;

  isShowManualLocation: boolean = false;
  hasInternationalSupport: boolean = false;
  isShowManualCustomerLocation: boolean = false;
  budgetValidation: boolean = false;
  syncingCurrency: boolean = false;
  checkDuplicacy: boolean = false;
  numberEdited: boolean = true;
  areaSizeUnits: Array<any>;
  propertyTypeList: Array<any>;
  agencyNameList: Array<any>;
  campaignList: Array<any>;
  channelPartnerList: Array<any>;
  carpetAreaConversion: string;
  versionNo: string = APP_VERSION;
  allUsers: any[];
  currency: any[] = [];
  profession: Array<Profession | string> = Object.values(Profession).slice(
    1,
    10
  );
  selectedIndex: number | null = null;
  profile: Profile;
  s3BucketUrl: string = env.s3ImageBucketURL;
  selectedFieldNames: any;
  subDomain: string = getTenantName();
  agencies: string;
  isTemplateExist: boolean = true;
  currentYear: number = new Date().getFullYear();
  userData: any;
  currentDate: Date = new Date();
  @ViewChild('contactNoInput') contactNoInput: any;
  @ViewChild('alternateNoInput') alternateNoInput: any;
  @ViewChild('referralNoInput') referralNoInput: any;
  @Input() qrFormData: any;
  screen = window;
  onPickerOpened = onPickerOpened;
  templateId: string;
  numbers: Array<{ display: string; value: number }> = NUMBER_10;
  numbers10: Array<{ display: string; value: number }> = NUMBER_10.slice(1);
  offerType: Array<{ displayName: string; value: number }> = OFFERING_TYPE;
  floorOptions: string[] = generateFloorOptions();
  furnishStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  isOpenPossessionModal: boolean = false;
  selectedPossession: string = '';
  selectedMonthAndYear: Date;
  isValidPossDate: boolean = false;
  selectedMonth: any;
  selectedYear: any;
  allowLandlineInput = allowLandlineInput;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;

  socialMedia: any = [
    {
      image: '../../../../../../assets/images/integration/facebook-logo.svg',
      name: 'facebook',
      baseUrl: 'https://www.facebook.com/',
    },
    {
      image: '../../../../../../assets/images/qr-form/instagram-logo.svg',
      name: 'instagram',
      baseUrl: 'https://www.instagram.com/',
    },
    {
      image: '../../../../../../assets/images/qr-form/linkedin-logo.svg',
      name: 'linkedin',
      baseUrl: 'https://www.linkedin.com/in/',
    },
    {
      image: '../../../../../../assets/images/qr-form/twitter-logo.svg',
      name: 'twitter',
      baseUrl: 'https://www.twitter.com/',
    },
    {
      image: '../../../../../../assets/images/integration/whatsapp-logo.svg',
      name: 'whatsapp',
      baseUrl: 'https://wa.me/',
    },
  ];
  nationalities: any[] = [];
  globalSettingsDetails: any;
  builtUpAreaConversion: string;
  saleableAreaConversion: string;
  propertyAreaConversion: string;
  netAreaConversion: string;
  @ViewChild('dateOfBirth') dateOfBirth: OwlDateTimeComponent<any>;
  gender:any[] = GENDER;
  maritalStatus:any[] = MARITAL_STATUS;

  
  get maxDate(): Date {
    const maxDate = new Date(this.currentDate);
    maxDate.setHours(0, 0, 0, 0);
    return maxDate;
  }

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    public router: Router,
    private route: ActivatedRoute
  ) {
    this.route.queryParams.subscribe((queryParams) => {
      if (queryParams['fbclid']) {
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true,
        });
      }
    });
    this.route.params.subscribe((params) => {
      this.templateId = params['templateId'];
    });
    this.store.dispatch(new FetchQRAreaUnitList());
    this.store.dispatch(new FetchGlobalSettingsAnonymous());
    // this.store.dispatch(new FetchQRProjectList());
    // this.store.dispatch(new FetchQRPropertyList());
    this.store.dispatch(new FetchAgencyNameListAnonymous());
    this.store.dispatch(new FetchChannelPartnerListAnonymous());
    this.store.dispatch(new FetchCampaignListAnonymous());
    this.store.dispatch(new FetchLocationsWithGoogle());

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this.addLeadForm = this.formBuilder.group({
      name: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      contactNo: ['', [this.contactNumberValidator('primary')]],
      alternateContactNo: ['', this.contactNumberValidator('alternative')],
      landLine: ['', ValidationUtil.landlineNumberValidator],
      email: ['', ValidationUtil.emailValidatorMinLength],
      leadSource: 'QR Code',
      lowerBudget: [null],
      currency: [this.defaultCurrency],
      upperBudget: [null],
      locationId: [null],
      enquiredLocality: null,
      enquiredSubCommunity: null,
      enquiredCommunity: null,
      enquiredTowerName: null,
      enquiredCity: null,
      enquiredState: null,
      enquiredCountry: [null],
      enquiredFor: null,
      propertyTypeId: [null],
      propSubType: [null],
      noOfBHK: [null],
      bhkType: [null],
      beds: [null],
      baths: [null],
      preferredFloors: [null],
      notes: '',
      projectId: [null],
      property: [null],
      agencies: [null],
      designation: [null],
      referralName: [''],
      referralContactNo: ['', this.contactNumberValidator('referral')],
      referralEmail: ['', ValidationUtil.emailValidatorMinLength],
      companyName: [''],
      possessionDate: [null],
      carpetArea: [null, Validators.pattern(/^[-+]?\d{0,7}(\.[0-9]{1,3})?$/)],
      carpetAreaUnitId: [null],
      builtUpArea: [null, Validators.pattern(/^[-+]?\d{0,7}(\.[0-9]{1,3})?$/)],
      builtUpAreaUnitId: [null],
      saleableArea: [null, Validators.pattern(/^[-+]?\d{0,7}(\.[0-9]{1,3})?$/)],
      saleableAreaUnitId: [null],
      propertyArea: [null, Validators.pattern(/^[-+]?\d{0,7}(\.[0-9]{1,3})?$/)],
      propertyAreaUnitId: [null],
      netArea: [null, Validators.pattern(/^[-+]?\d{0,7}(\.[0-9]{1,3})?$/)],
      netAreaUnitId: [null],
      unitName: [null],
      profession: null,
      address: [null],
      customerLocationId: [null],
      customerLocality: null,
      customerSubCommunity: null,
      customerCommunity: null,
      customerTowerName: null,
      customerCity: null,
      customerState: null,
      customerCountry: null,
      channelPartnerList: [null],
      campaigns: [null],
      furnishStatus: [null],
      offeringType: [null],
      clusterName: [null],
      nationality: [null],
      purpose: [null],
      dateOfBirth: [null],
      gender: [null],
      maritalStatus: [null],
    });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.shouldEnablePropertyListing) {
          this.store.dispatch(new FetchQRPropertyTypesList('listing'));
        } else {
          this.store.dispatch(new FetchQRPropertyTypesList());
        }
        this.globalSettingsDetails = data;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.addLeadForm.get('currency').patchValue(this.defaultCurrency);
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
        this.currency = data?.countries?.length
          ? data.countries[0].currencies
          : null;
      });
    this.nationalities = Object.values(countries).map((country: any) => ({
      name: country.name,
    }));
    this.store
      .select(getQRFormById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.qrFormData = data?.data;
        if (data?.message?.includes('Template not found by this Id')) {
          this.isTemplateExist = false;
        }
        this.selectedFieldNames = data?.data?.content?.map(
          (field: any) => field.displayName
        );
      });

    this.addLeadForm.controls['propertyTypeId'].valueChanges.subscribe(
      (val: string) => {
        this.propSubTypes = setPropertySubTypeList(val, this.propertyTypeList);
      }
    );

    this.store
      .select(getQrAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.store
      .select(getQrPropertyTypes)
      .pipe(takeUntil(this.stopper))
      .subscribe((qrType: any) => {
        this.propertyTypeList = qrType || [];
      });

    this.store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.store
      .select(getCampaignListAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((campaigns: any) => {
        this.campaignList = campaigns?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getAgencyNameListAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((agencies: any) => {
        this.agencyNameList = agencies?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getChannelPartnerListAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((channelPartners: any) => {
        this.channelPartnerList = channelPartners
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.addLeadForm
      .get('propertyTypeId')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(VALIDATION_SET, this.addLeadForm, 'propSubType', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.addLeadForm, 'propSubType');
        }
        this.addLeadForm.patchValue({
          propSubType: null,
        });
      });

    this.addLeadForm.get('carpetArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(VALIDATION_SET, this.addLeadForm, 'carpetAreaUnitId', [
          Validators.required,
        ]);
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'carpetAreaUnitId'
        );
        this.addLeadForm.patchValue({
          carpetAreaUnitId: null,
        });
      }
    });

    this.addLeadForm
      .get('carpetAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.carpetAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });

    this.addLeadForm.get('builtUpArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'builtUpAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'builtUpAreaUnitId'
        );
        this.addLeadForm.patchValue({
          builtUpAreaUnitId: null,
        });
      }
    });
    this.addLeadForm
      .get('builtUpAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.builtUpAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });

    this.addLeadForm.get('saleableArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'saleableAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'saleableAreaUnitId'
        );
        this.addLeadForm.patchValue({
          saleableAreaUnitId: null,
        });
      }
    });

    this.addLeadForm
      .get('saleableAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.saleableAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addLeadForm.get('propertyArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'propertyAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'propertyAreaUnitId'
        );
        this.addLeadForm.patchValue({
          propertyAreaUnitId: null,
        });
      }
    });

    this.addLeadForm
      .get('propertyAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.propertyAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addLeadForm.get('netArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(VALIDATION_SET, this.addLeadForm, 'netAreaUnitId', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.addLeadForm, 'netAreaUnitId');
        this.addLeadForm.patchValue({
          netAreaUnitId: null,
        });
      }
    });

    this.addLeadForm.get('netAreaUnitId').valueChanges.subscribe((val: any) => {
      this.areaSizeUnits.filter((areaUnitId: any) => {
        if (areaUnitId.id == val) {
          this.netAreaConversion = areaUnitId.conversionFactor;
        }
      });
    });

    this.addLeadForm.get('currency')?.valueChanges.subscribe((value) => {
      if (!this.syncingCurrency) {
        this.syncingCurrency = true;
        this.addLeadForm.get('currency').setValue(value);
        this.syncingCurrency = false;
      }
    });

    this.addLeadForm.get('currency').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        this.addLeadForm.value.lowerBudget,
        val
      );
      this.upperBudgetInWords = formatBudget(
        this.addLeadForm.value.upperBudget,
        val
      );
    });

    this.addLeadForm.get('lowerBudget').valueChanges?.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        val,
        this.addLeadForm.value.currency
      );
      if (!this.addLeadForm.value.upperBudget) {
        this.addLeadForm.value.upperBudget = val;
      } else {
        val > this.addLeadForm.get('upperBudget').value
          ? (this.budgetValidation = true)
          : (this.budgetValidation = false);
      }
    });

    this.addLeadForm.get('upperBudget').valueChanges?.subscribe((val) => {
      this.upperBudgetInWords = formatBudget(
        val,
        this.addLeadForm.value.currency
      );
      this.addLeadForm.get('lowerBudget').value > val
        ? (this.budgetValidation = true)
        : (this.budgetValidation = false);
    });

    /* Fetching Places List */
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this.store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });
  }

  ngOnInit() {
    this.route.queryParams.subscribe((queryParams) => {
      if (queryParams['fbclid']) {
        this.router
          .navigate([], {
            relativeTo: this.route,
            queryParams: {},
            replaceUrl: true,
          })
          .then(() => {
            this.fetchQrFormData();
          });
      } else {
        this.fetchQrFormData();
      }
    });
    this.onUnitChange('carpetAreaUnitId');
    // Add possession-related form controls
    this.addLeadForm.addControl('possessionType', this.formBuilder.control(null));
    this.addLeadForm.addControl('customPossessionDate', this.formBuilder.control(null));
    this.addLeadForm.addControl('globalRange', this.formBuilder.control(null));
    this.addLeadForm.addControl('globalDate', this.formBuilder.control(null));
    this.addLeadForm.get('possessionDate').valueChanges.subscribe((value) => {
      if (value) {
        this.convertDateToMonth(value);
      }
    });
    this.addLeadForm.get('globalRange').valueChanges.subscribe((value) => {
      if (value) {
        this.handlePossessionRangeChange(value);
      }
    });
    this.store.dispatch(new FetchProjectIdWithName());
    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any[]) => {
        if (this.projectList?.length === 0) {
          this.projectList = res
            ?.filter((data: any) => data.name)
            .slice()
            .sort((a: any, b: any) => a.name.localeCompare(b.name));
        }
      });
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this.store.dispatch(new FetchAssignedProjectsList(this.templateId));
    this.store
      .select(getAssignedProjects)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any[]) => {
        if (res?.length > 1) {
          this.projectList = [...res];
        } else if (res?.length == 1) {
          this.projectList = [...res];
          this.addLeadForm.get('projectId').setValue(res[0].id);
        }
      });
    const allUsers$ = this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      allUsers: allUsers$,
    }).subscribe(({ allUsers }) => {
      this.allUsers = allUsers?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });
    });
  }

  get isResidential(): boolean {
    return this.addLeadForm.get('propertyTypeId').value === 'Residential';
  }

  hasSocialMedia(platform: string): boolean {
    return (
      this.qrFormData?.socialMedias.some(
        (item: any) =>
          item.socialMediaPlatform === platform && item.socialMediaId // Assuming that socialMediaId should be truthy
      ) ?? false
    );
  }

  onUnitChange(unit: any) {
    const carpetAreaUnitId = this.addLeadForm.get(unit).value;
    this.addLeadForm.controls[unit]?.setValue(null);
    if (
      !this.addLeadForm.get('saleableAreaUnitId').value &&
      !this.addLeadForm.get('builtUpAreaUnitId').value &&
      !this.addLeadForm.get('carpetAreaUnitId').value &&
      !this.addLeadForm.get('propertyAreaUnitId').value &&
      !this.addLeadForm.get('netAreaUnitId').value
    ) {
      this.addLeadForm.controls['saleableAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addLeadForm.controls['propertyAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addLeadForm.controls['netAreaUnitId'].setValue(carpetAreaUnitId);
      this.addLeadForm.controls['builtUpAreaUnitId'].setValue(carpetAreaUnitId);
      this.addLeadForm.controls['carpetAreaUnitId'].setValue(carpetAreaUnitId);
    } else {
      this.addLeadForm.controls[unit].setValue(carpetAreaUnitId);
    }
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  getSelectedCountryCodeAlternateNo(): any {
    return this.alternateNoInput?.selectedCountry;
  }

  getSelectedCountryCodeReferralNo(): any {
    return this.referralNoInput?.selectedCountry;
  }



  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (numType == 'primary') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      } else if (numType == 'alternative') {
        const input = document.querySelector(
          '.alternateNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeAlternateNo()?.dialCode;
      } else if (numType == 'referral') {
        const input = document.querySelector(
          '.referralNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeReferralNo()?.dialCode;
      }

      try {
        let phoneValue = '';
        if (numType === 'primary') {
          phoneValue = this.contactNoInput?.value;
        } else if (numType === 'alternative') {
          phoneValue = this.alternateNoInput?.value;
        } else if (numType === 'referral') {
          phoneValue = this.referralNoInput?.value;
        }

        const validNumber = isPossiblePhoneNumber(
          phoneValue || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  getSocialMediaLink(platform: string): string {
    const socialMediaId = this.getSocialMediaId(this.qrFormData, platform);
    if (socialMediaId) {
      return `${socialMediaId}`;
    }
    return '#';
  }

  getSocialMediaId(item: any, platform: string): string | null {
    const socialMediaEntry = item?.socialMedias?.find(
      (entry: any) => entry.socialMediaPlatform === platform
    );
    return socialMediaEntry ? socialMediaEntry.socialMediaId : null;
  }

  shouldDisplayEnquiryInfo(): boolean {
    const fieldsToCheck = QR_DISPLAY_FIELDS.map((field) => field.controlName);
    return fieldsToCheck.some(
      (field) => this.selectedFieldNames?.indexOf(field) !== -1
    );
  }

  getBhkNo(bhk: string) {
    return BHK_NO_ALL[this.noOfBhk?.indexOf(bhk)];
  }

  postData() {
    if (
      this.addLeadForm.value.lowerBudget &&
      !this.addLeadForm.value.upperBudget
    ) {
      this.addLeadForm.value.upperBudget = this.addLeadForm.value.lowerBudget;
    } else if (
      !this.addLeadForm.value.lowerBudget &&
      this.addLeadForm.value.upperBudget
    ) {
      this.addLeadForm.value.lowerBudget = this.addLeadForm.value.upperBudget;
    }

    if (
      !this.addLeadForm.valid ||
      this.addLeadForm.value.lowerBudget > this.addLeadForm.value.upperBudget
    ) {
      validateAllFormFields(this.addLeadForm);
      return;
    }
    const leadData = this.addLeadForm.value;
    const templateId = this.qrFormData?.id;
    let addLeadData = {
      ...leadData,
      name: leadData.name.trim(),
      contactNo: leadData.contactNo
        ? `${leadData.contactNo?.toString()}`
        : null,
      alternateContactNo: leadData.alternateContactNo
        ? `${leadData.alternateContactNo?.toString()}`
        : null,
      countryCode: leadData.contactNo ? this.getSelectedCountryCodeContactNo()?.dialCode : null,
      altCountryCode: leadData.alternateContactNo ? this.getSelectedCountryCodeAlternateNo()?.dialCode : null,
      referralContactNo: leadData.referralContactNo
        ? `${leadData.referralContactNo?.toString()}`
        : null,
      email: leadData?.email,
      notes: leadData?.notes,
      projectId: leadData?.projectId,
      projectsList: null,
      propertiesList: [leadData.property],
      referralName: leadData?.referralName,
      leadSource: 23,
      nationality: leadData?.nationality,
      enquiry: {
        bhkTypes:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType != 'Plot'
            ? leadData.bhkType?.map((bhkType: any) => BHKType[bhkType])
            : [],
        bhKs:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType !== 'Plot'
            ? (leadData?.noOfBHK || [])
              .filter((bhkNo: any) => bhkNo != null && parseFloat(bhkNo) > 0)
              ?.map((bhkNo: any) => parseFloat(bhkNo))
              .sort((a: number, b: number) => a - b)
            : [],
        beds: this.addLeadForm.get('propertyTypeId').value === 'Residential' && leadData.propSubType !== 'Plot' ? leadData?.beds
          ?.filter((bed: any) => bed != null)
          .slice()
          .sort((a: any, b: any) => String(a).localeCompare(String(b)))
          : [],
        baths:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType !== 'Plot'
            ? leadData?.baths
              ?.filter((bed: any) => bed != null)
              .slice()
              .sort((a: any, b: any) => String(a).localeCompare(String(b)))
            : [],
        furnished: this.addLeadForm.get('propertyTypeId')?.value && this.addLeadForm.get('propertyTypeId').value !== 'Agricultural' && leadData.furnishStatus
          ? FurnishStatus[leadData.furnishStatus]
          : null,
        floors: this.addLeadForm.get('propertyTypeId')?.value && this.addLeadForm.get('propertyTypeId').value !== 'Agricultural' && leadData.preferredFloors ? leadData.preferredFloors : null,
        offerType: leadData.offeringType ?? null,
        purpose: leadData.purpose ?? null,
        enquiryTypes:
          leadData.enquiredFor?.map((enquiry: any) => EnquiryType[enquiry]) ||
          [],
        lowerBudget: leadData?.lowerBudget ? leadData?.lowerBudget : '0',
        upperBudget: leadData?.upperBudget ? leadData?.upperBudget : '0',
        currency: leadData?.currency
          ? leadData?.currency
          : this.defaultCurrency,
        possessionDate:
          leadData?.globalRange === 'Under Construction'
            ? null
            : leadData?.globalRange === 'Custom Date' ||
              leadData?.globalRange === '6 Months' ||
              leadData?.globalRange === '1 Year' ||
              leadData?.globalRange === '2 Years'
              ? leadData?.globalDate
                ? setTimeZoneDate(
                  leadData.globalDate,
                  this.userData?.timeZoneInfo?.baseUTcOffset
                )
                : null
              : leadData.possessionDate
                ? setTimeZoneDate(
                  leadData.possessionDate,
                  this.userData?.timeZoneInfo?.baseUTcOffset
                )
                : undefined,
        carpetArea: leadData?.carpetArea || null,
        carpetAreaUnitId:
          leadData?.carpetArea && leadData?.carpetAreaUnitId
            ? leadData.carpetAreaUnitId
            : null,
        conversionFactor:
          leadData?.carpetArea && leadData.carpetAreaUnitId
            ? Number(this.carpetAreaConversion)
            : null,
        builtUpArea: leadData.builtUpArea || null,
        builtUpAreaUnitId:
          leadData?.builtUpArea && leadData?.builtUpAreaUnitId
            ? leadData?.builtUpAreaUnitId
            : null,
        builtUpAreaConversionFactor:
          leadData?.builtUpArea && leadData?.builtUpAreaUnitId
            ? Number(this.builtUpAreaConversion)
            : null,
        saleableArea: leadData?.saleableArea || null,
        saleableAreaUnitId:
          leadData?.saleableArea && leadData?.saleableAreaUnitId
            ? leadData.saleableAreaUnitId
            : null,
        saleableAreaConversionFactor:
          leadData?.saleableArea && leadData?.saleableAreaUnitId
            ? Number(this.saleableAreaConversion)
            : null,
        propertyArea: leadData?.propertyArea || null,
        propertyAreaUnitId:
          leadData?.propertyArea && leadData?.propertyAreaUnitId
            ? leadData?.propertyAreaUnitId
            : null,
        propertyAreaConversionFactor:
          leadData?.propertyArea && leadData?.propertyAreaUnitId
            ? Number(this.propertyAreaConversion)
            : null,

        netArea: leadData?.netArea || null,
        netAreaUnitId:
          leadData?.netArea && leadData?.netAreaUnitId
            ? leadData?.netAreaUnitId
            : null,
        netAreaConversionFactor:
          leadData?.netArea && leadData?.netAreaUnitId
            ? Number(this.netAreaConversion)
            : null,
        unitName: leadData?.unitName,
        clusterName: leadData?.clusterName,
        propertyTypeIds: getPropertyTypeIds(
          this.propertyTypeList,
          leadData.propertyTypeId,
          leadData.propSubType
        ),
        addresses: [
          {
            locationId:
              leadData.locationId?.locationId ??
              leadData.locationId?.locationId,
            placeId:
              leadData.locationId?.placeId ?? leadData.locationId?.placeId,
            subLocality: leadData.enquiredLocality ?? leadData.enquiredLocality,
            city: leadData.enquiredCity ?? leadData.enquiredCity,
            state: leadData.enquiredState ?? leadData.enquiredState,
            subCommunity:
              leadData.enquiredSubCommunity ?? leadData.enquiredSubCommunity,
            community: leadData.enquiredCommunity ?? leadData.enquiredCommunity,
            towerName: leadData.enquiredTowerName ?? leadData.enquiredTowerName,
            country: leadData.enquiredCountry ?? leadData.enquiredCountry,
          },
        ],
      },
      profession: leadData.profession ? Profession[leadData?.profession] : 0,
      address: {
        locationId:
          leadData.customerLocationId?.locationId ??
          leadData.customerLocationId?.locationId,
        placeId:
          leadData.customerLocationId?.placeId ??
          leadData.customerLocationId?.placeId,
        subLocality: leadData.customerLocality ?? leadData.customerLocality,
        subCommunity:
          leadData.customerSubCommunity ?? leadData.customerSubCommunity,
        community: leadData.customerCommunity ?? leadData.customerCommunity,
        towerName: leadData.customerTowerName ?? leadData.customerTowerName,
        city: leadData.customerCity ?? leadData.customerCity,
        state: leadData.customerState ?? leadData.customerState,
        country: leadData.customerCountry ?? leadData.customerCountry,
      },
      channelPartnerList: [leadData?.channelPartnerList],
      agencies: leadData?.agencies ? [{ name: leadData.agencies }] : null,
      companyName: leadData?.companyName,
      campaigns: leadData?.campaigns ? [{ name: leadData.campaigns }] : null,
      designation: leadData?.designation,
      possesionType: leadData?.globalRange
        ? this.getPossessionTypeEnum(leadData.globalRange)
        : 0,
      dateOfBirth: leadData.dateOfBirth,
      gender: leadData.gender,
      maritalStatus: leadData.maritalStatus,
    };
    if (templateId) {
      this.store.dispatch(new AddQRLead(addLeadData, templateId));
    }
    const projectValue = this.addLeadForm.get('projectId').value;
    this.addLeadForm.reset();
    this.addLeadForm.get('currency').patchValue(this.defaultCurrency);
    if (this.projectList?.length == 1) {
      this.addLeadForm.patchValue({ projectId: projectValue });
    }

    this.selectedPossession = null;
    this.selectedMonth = null;
    this.selectedYear = null;
    this.selectedMonthAndYear = null;
    this.isOpenPossessionModal = false;

    this.resetNumber = true;
    setTimeout(() => {
      this.resetNumber = false;
    }, 0);
  }

  fetchQrFormData() {
    if (this.templateId) {
      this.store.dispatch(new FetchQrFormById(this.templateId));
    }
  }

  convertDateToMonth(data: any) {
    if (!data) return;
    this.selectedMonthAndYear = new Date(data);
    this.selectedMonth = MONTHS[this.selectedMonthAndYear.getMonth()];
    this.selectedYear = this.selectedMonthAndYear.getFullYear();
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  monthChanged(event: Date): void {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.addLeadForm.controls['globalDate'].setValue(
      this.selectedMonthAndYear
    );
    this.isValidPossDate = false;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.dt5.close();
  }

  onPossessionChange(event: { possessionType: number | null; customDate: string | null; displayValue: string }): void {
    this.addLeadForm.patchValue({
      possessionType: event.possessionType,
      customPossessionDate: event.customDate
    });
  }

  closeModal() {
    if (
      this.addLeadForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossDate = true;
      return;
    }
    this.isOpenPossessionModal = false;
  }

  handlePossessionRangeChange(value: string): void {
    this.addLeadForm.get('globalRange')?.setValue(value);
    if (value === 'Custom Date') {
      this.convertDateToMonth(
        this.addLeadForm.get('globalDate').value ?? this.addLeadForm.get('possessionDate').value
      );
      if (!this.selectedMonthAndYear) {
        this.isOpenPossessionModal = true;
      }
    } else if (
      value === '6 Months' ||
      value === '1 Year' ||
      value === '2 Years'
    ) {
      this.calculateDateRange(value);
      const endDate = this.addLeadForm.get('globalDate').value;
      this.addLeadForm.controls['possessionDate'].setValue(endDate);
      this.addLeadForm.controls['possessionDate'].markAsTouched();

      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isValidPossDate = false;
      this.isOpenPossessionModal = false;
    } else if (value === 'Under Construction') {
      this.addLeadForm.controls['globalDate'].setValue(null);
      this.addLeadForm.controls['possessionDate'].setValue(null);
      this.selectedPossession = value;
      this.isOpenPossessionModal = false;
    }
  }

  calculateDateRange(value: string): void {
    const currentDate = new Date(this.currentDate);
    let startDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      1
    );
    let endDate: Date;

    if (value === '6 Months') {
      endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);
    } else if (value === '1 Year') {
      endDate = new Date(startDate.getFullYear() + 1, startDate.getMonth(), 0);
    } else if (value === '2 Years') {
      endDate = new Date(startDate.getFullYear() + 2, startDate.getMonth(), 0);
    }

    this.addLeadForm.controls['globalDate'].setValue(endDate);
  }

  getPossessionTypeEnum(possessionTypeText: string): number {
    switch (possessionTypeText) {
      case 'Under Construction':
        return PossessionType['Under Construction'];
      case '6 Months':
        return PossessionType['6 Months'];
      case '1 Year':
        return PossessionType['1 Year'];
      case '2 Years':
        return PossessionType['2 Years'];
      case 'Custom Date':
        return PossessionType['Custom Date'];
      default:
        return PossessionType.None;
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
