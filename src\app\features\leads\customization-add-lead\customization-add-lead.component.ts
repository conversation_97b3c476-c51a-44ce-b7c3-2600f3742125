import {
  Component,
  EventEmitter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { countries } from 'countries-list';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BehaviorSubject, combineLatest } from 'rxjs';

import {
  debounceTime,
  distinctUntilChanged,
  filter,
  skipWhile,
  take,
  takeUntil,
} from 'rxjs/operators';

import {
  BHK_NO_ALL,
  BHK_TYPE,
  EMPTY_GUID,
  FURNISH_STATUS,
  GENDER,
  MARITAL_STATUS,
  MONTHS,
  NUMBER_10,
  OFFERING_TYPE,
  POSSESSION_DATE_FILTER_LIST,
  PROPERTY_TYPE_LIST,
  PURPOSE_LIST,
  TRANSACTION_TYPE_LIST,
  VALIDATION_CLEAR,
  VALIDATION_SET
} from 'src/app/app.constants';
import {
  BHKType,
  EnquiryType,
  FurnishStatus,
  IntegrationSource,
  LeadSource,
  PossessionType,
  Profession
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { LeadSourceType } from 'src/app/core/interfaces/master-data.interface';
import {
  allowLandlineInput,
  assignToSort,
  changeCalendar,
  convertUrlsToLinks,
  formatBudget,
  generateFloorOptions,
  getAssignedToDetails,
  getBHKDisplayString,
  getFormattedLocation,
  getLocalityDetailsByObj,
  getLocationDetailsByObj,
  getPropertyTypeIds,
  getTimeZoneDate,
  isEmptyObject,
  onPickerOpened,
  onlyNumbers,
  patchFormControlValue,
  patchTimeZoneDate,
  setPropertySubTypeList,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getAllSources,
  getAllSourcesLoading,
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  AddLead,
  FetchAgencyNameList,
  FetchCampaignList,
  FetchChannelPartnerList,
  FetchLeadById,
  FetchLeadIdSuccess,
  FetchProjectList,
  FetchPropertyList,
  FetchSubSourceList,
  HasLeadAltInfo,
  HasLeadInfo,
  UpdateLead,
  cancelAddLead,
} from 'src/app/reducers/lead/lead.actions';
import {
  getActiveLead,
  getActiveLeadIsLoading,
  getAgencyNameList,
  getAgencyNameListIsLoading,
  getCampaignList,
  getCampaignListIsLoading,
  getChannelPartnerList,
  getChannelPartnerListIsLoading,
  getLeadListIsPosting,
  getLeads,
  getProjectList,
  getProjectListIsLoading,
  getPropertyList,
  getPropertyListIsLoading,
  getSubSourceList,
  getSubSourceListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import {
  FetchAreaUnitList,
  FetchLeadSourceList,
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnitIsLoading,
  getAreaUnits,
  getLeadSource,
  getLeadSourceIsLoading,
} from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchInternationalLocations,
  FetchLocationsWithGoogle,
  removeLocationsWithGoogleApi,
} from 'src/app/reducers/site/site.actions';
import {
  getInternationLocationsIsLoading,
  getInternationalLocations,
  getLocationsWithGoogleApi,
  getLocationsWithGoogleApiIsLoading,
} from 'src/app/reducers/site/site.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';

@Component({
  selector: 'customization-add-lead',
  templateUrl: './customization-add-lead.component.html',
})
export class CustomizationAddLeadComponent implements OnInit, OnDestroy {
  @ViewChild('previewModal') previewModal: TemplateRef<any>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  selectedLeadId: any;
  selectedLeadInfo: any;
  addLeadForm: FormGroup;
  loggedInUserId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
  propertyTypeList: any[] = JSON.parse(localStorage.getItem('propertyType'));
  enquiredForList: Array<Object> = TRANSACTION_TYPE_LIST;
  purposeList: Array<{ displayName: string; value: number }> = PURPOSE_LIST;
  placesList: any[] = [];
  projectList: Array<string> = [];
  propertyList: Array<string> = [];
  agencyNameList: Array<string> = [];
  propSubTypes: Array<{ displayName: string }> = [];
  propTypeImg: {} = PROPERTY_TYPE_LIST;
  bhkTypes: Array<string> = BHK_TYPE;
  bhkNoList: Array<string> = BHK_NO_ALL;
  numbers: Array<{ display: string; value: number }> = NUMBER_10;
  numbers10: Array<{ display: string; value: number }> = NUMBER_10.slice(1);
  offerType: Array<{ displayName: string; value: number }> = OFFERING_TYPE;
  floorOptions: string[] = generateFloorOptions();
  furnishStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  leadSources: any[] = [];
  preferredCountries = ['in'];
  checkDuplicacy: boolean = false;
  checkAlternateNumDuplicacy: boolean = false;
  duplicateLead: any;
  duplicateAltLead: any;
  currFormValues: any;
  lowerBudgetInWords: string = '';
  upperBudgetInWords: string = '';
  hasInternationalSupport: boolean = false;
  canEdit: boolean = false;
  canUpdateInfo: boolean = false;
  canAssign: boolean = false;
  canViewComponent: boolean = false;
  numberEdited: boolean = true;
  alternateNumberEdited: boolean = true;
  budgetValidation: boolean = false;
  users: any;
  allUsers: any[];
  currencyList: any[] = [];
  defaultCurrency: string;
  countryCode: any[];
  timeZone: any[];
  primaryAgentList: Object[];
  secondaryAgentList: Object[];
  primaryUserList: Object[];
  secondaryUserList: Object[];
  subSourceList: any;
  subSources: any;
  isShowManualLocation: boolean = false;
  isShowManualCustomerLocation: boolean = false;
  syncingCurrency: boolean = false;
  areaSizeUnits: Array<any>;
  carpetAreaConversion: string;
  builtUpAreaConversion: string;
  saleableAreaConversion: string;
  propertyAreaConversion: string;
  netAreaConversion: string;
  isGlobalSourceEdit: boolean;
  canAdminEditSource: boolean;
  canEditSource: boolean = true;
  profession: Array<Profession | string> = Object.values(Profession).slice(
    1,
    10
  );
  channelPartnerList: Array<any>;
  campaigns: Array<any>;
  isCampaignListLoading: boolean;
  patchFormControlValue = patchFormControlValue;
  isEmptyObject = isEmptyObject;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  getBHKDisplayString = getBHKDisplayString;
  getLocationDetailsByObj = getLocationDetailsByObj;
  getFormattedLocation = getFormattedLocation;
  isLeadSourceListLoading: boolean = true;
  isSubSourceListLoading: boolean = true;
  isAreaUnitLoading: boolean = true;
  projectListIsLoading: boolean = true;
  propertyListIsLoading: boolean = true;
  isAgencyNameListLoading: boolean = true;
  isChannelPartnerListLoading: boolean = true;
  isAllUsersLoading: boolean = true;
  isAdminAndReporteesLoading: boolean = true;
  isPostingData: boolean = false;
  activeLeadIsLoading: boolean = true;
  isNotesMandatory: boolean;
  isDualOwnershipEnabled: boolean = true;
  canViewLeadSource: boolean = false;
  isGlobalSettingsLoading: boolean = true;
  scrollpause: boolean = false;
  manualLocationsList: any[] = [];
  backupLocation: any[] = [];
  @ViewChild('contactNoInput') contactNoInput: any;
  @ViewChild('alternateNoInput') alternateNoInput: any;
  @ViewChild('referralNoInput') referralNoInput: any;
  receivedCurrentPath: string;
  inactiveUsers: any;
  activeUsers: any;
  isPlacesListLoading: any;
  isBackSpace: boolean;
  userBasicDetails: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  globalSettingsDetails: any;
  currentActive: any = 0;
  isHideBasicInfo: boolean = false;
  isHideMaritalInfo: boolean = false;
  isHideAdditionalInfo: boolean = false;
  isHideEnquiryInfo: boolean = false;
  isHideOtherInfo: boolean = false;
  isHideNRIInfo: boolean = false;
  fieldSections: any = [
    {
      name: 'Lead Info',
      isHide: false,
      icon: 'ic-notes-pen',
    },
    {
      name: 'Enquiry Info',
      isHide: false,
      icon: 'ic-circle-exclamation-solid',
    },
    {
      name: 'Additional Info',
      isHide: false,
      icon: 'ic-file-clip',
    },
    {
      name: 'Others',
      isHide: false,
      icon: 'ic-hexagon-arrow',
    },
    {
      name: 'Notes',
      isHide: false,
      icon: 'ic-notes-solid',
    },
  ];
  showLeftNav: boolean = true;
  isCreateDuplicateLeads: boolean = false;
  isAllowDuplicate: boolean = false;
  internationalLocations: any[] = [];
  isInternationalLocationLoading: boolean = false;
  isduplicateLead: boolean = false;
  nationalities: any[] = [];
  isOpenPossessionModal: boolean = false;
  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  selectedMonthAndYear: Date;
  isValidPossDate: boolean;
  selectedMonth: any;
  selectedYear: any;
  selectedPossession: any;
  allowLandlineInput: any = allowLandlineInput;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;
  @ViewChild('dt2') dt2: OwlDateTimeComponent<any>;
  gender: any[] = GENDER;
  maritalStatus: any[] = MARITAL_STATUS;

  convertUrlsToLinks = convertUrlsToLinks
  isSourcesLoading: boolean;
  get isAddLead(): boolean {
    return !this.selectedLeadId;
  }

  get isSelectedLeadInfoFetched(): boolean {
    return this.selectedLeadInfo ? true : false;
  }

  get maxDate(): Date {
    const maxDate = new Date(this.currentDate);
    maxDate.setHours(0, 0, 0, 0);
    return maxDate;
  }

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private shareDataService: ShareDataService,
    private modalService: BsModalService,
    public trackingService: TrackingService,
    public modalRef: BsModalRef,
  ) { }

  ngOnInit() {
    this.nationalities = Object.values(countries).map((country: any) => ({ name: country.name }));

    this.shareDataService.URL$.subscribe((data) => {
      this.receivedCurrentPath = data;
    });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.propertyTypeList = JSON.parse(localStorage.getItem('propertyType'));
    this.trackingService.trackFeature(`Web.Leads.Button.AddLead.View`);
    this.addLeadForm = this.formBuilder.group({
      name: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      contactNo: ['', [this.contactNumberValidator('primary')]],
      alternateContactNo: ['', this.contactNumberValidator('alternative')],
      landLine: ['', ValidationUtil.landlineNumberValidator],
      email: ['', ValidationUtil.emailValidatorMinLength],
      leadSource: 'Direct',
      subSource: [null],
      lowerBudget: [null],
      currency: [this.defaultCurrency],
      upperBudget: [null],
      assignTo: null,
      secondaryAssignTo: [null],
      enquiredLocationId: [null],
      enquiredLocality: null,
      enquiredSubCommunity: null,
      enquiredCommunity: null,
      enquiredTowerName: null,
      enquiredCity: null,
      enquiredState: null,
      enquiredCountry: null,
      enquiredPincode: null,
      enquiredFor: null,
      propertyTypeId: [null],
      propSubType: [null],
      noOfBHK: [null],
      bhkType: [null],
      beds: [null],
      baths: [null],
      preferredFloors: [null],
      notes: '',
      projectsList: [null],
      propertiesList: [null],
      agencies: [null],
      referralName: [''],
      referralContactNo: ['', this.contactNumberValidator('referral')],
      referralEmail: ['', ValidationUtil.emailValidatorMinLength],
      companyName: [null],
      designation: [null],
      possessionDate: [null],
      carpetArea: [null],
      carpetAreaUnitId: [null],
      builtUpArea: [null],
      builtUpAreaUnitId: [null],
      saleableArea: [null],
      saleableAreaUnitId: [null],
      propertyArea: [null],
      propertyAreaUnitId: [null],
      netArea: [null],
      netAreaUnitId: [null],
      unitName: [null],
      nationality: [null],
      clusterName: [null],
      closingManager: [null],
      sourcingManager: [null],
      profession: null,
      address: [null],
      customerLocationId: [null],
      customerLocality: null,
      customerSubCommunity: null,
      customerCommunity: null,
      customerTowerName: null,
      customerCity: null,
      customerState: null,
      customerCountry: null,
      customerPincode: null,
      channelPartnerList: [null],
      campaigns: [null],
      furnishStatus: [null],
      offeringType: [null],
      purpose: null,
      possessionRange: [null],
      customPossessionDate: [null],
      possessionType: [null],
      gender: [null],
      dateOfBirth: [null],
      maritalStatus: [null],
    });

    this.store.dispatch(new FetchLeadSourceList());
    this.store.dispatch(new FetchAdminsAndReportees());
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store.dispatch(new FetchProjectList());
    this.store.dispatch(new FetchPropertyList());
    this.store.dispatch(new FetchSubSourceList());
    this.store.dispatch(new FetchAgencyNameList());
    this.store.dispatch(new FetchAreaUnitList());
    this.store.dispatch(new FetchChannelPartnerList());
    this.store.dispatch(new FetchCampaignList());

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.isCustomLeadFormEnabled) {
          this.store.dispatch(new FetchInternationalLocations());
        }

        this.isAllowDuplicate =
          data?.duplicateFeatureInfo?.allowAllDuplicates &&
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.globalSettingsDetails = data;
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;
        this.isGlobalSourceEdit = data?.isLeadSourceEditable;
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
        this.timeZone = data?.defaultTimeZone;
        this.isNotesMandatory =
          data?.leadNotesSetting?.isNotesMandatoryOnAddLead;
        if (this.isNotesMandatory) {
          this.addLeadForm?.controls['notes']?.setValidators([
            Validators.required,
          ]);
        } else {
          this.addLeadForm?.controls['notes']?.clearValidators();
        }
        this.addLeadForm?.patchValue({
          currency:
            this.selectedLeadInfo?.enquiry?.currency || this.defaultCurrency,
        });
      });

    this.store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isGlobalSettingsLoading = isLoading;
      });
    /* Fetching Places List */
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => {
          if (this.isBackSpace) {
            return true;
          } else {
            return searchStr?.length > 2;
          }
        })
      )
      .subscribe((searchStr: string) => {
        if (this.globalSettingsDetails?.isCustomLeadFormEnabled) {
          this.store.dispatch(new FetchInternationalLocations(searchStr));
        } else {
          this.store.dispatch(new FetchLocationsWithGoogle(searchStr));
        }
      });

    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.selectedLeadId = params.id;
        this.store
          .select(getLeads)
          .pipe(take(1))
          .subscribe((leads: any) => {
            const activeLead = leads.filter(
              (lead: any) => this.selectedLeadId === lead?.id
            )?.[0];
            if (activeLead) {
              this.store.dispatch(new FetchLeadIdSuccess({ ...activeLead }));
            } else {
              this.store.dispatch(new FetchLeadById(this.selectedLeadId));
            }
          });
      }
    });

    this.headerTitle.setLangTitle(
      this.selectedLeadId ? 'BUTTONS.edit-lead' : 'BUTTONS.add-lead'
    );

    this.addLeadForm.controls['propertyTypeId'].valueChanges.subscribe(
      (val: string) => {
        this.propSubTypes = setPropertySubTypeList(val, this.propertyTypeList);
      }
    );

    this.addLeadForm.controls['possessionRange'].valueChanges.subscribe(
      (value) => {
        if (value === 'Custom Date') {
          const possessionDate = this.addLeadForm.get('customPossessionDate').value || this.selectedLeadInfo?.enquiry?.possessionDate || null;
          if (possessionDate) {
            this.convertDateToMonth(possessionDate);
          } else {
            this.selectedMonthAndYear = null;
            this.selectedMonth = null;
            this.selectedYear = null;
            this.selectedPossession = null;
          }
        } else if (
          value === '6 Months' ||
          value === '1 Year' ||
          value === '2 Years'
        ) {
          this.calculateDateRange(value);
          this.selectedPossession = null;
          this.isValidPossDate = false;
        } else {
          // Reset validation if any other option is selected
          this.isValidPossDate = false;
        }
      }
    );

    this.store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subSourceList = data;
        if (this.isAddLead) {
          this.updateSubSource('Direct');
        }
      });

    this.store
      .select(getSubSourceListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSubSourceListLoading = isLoading;
      });

    this.store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAreaUnitLoading = isLoading;
      });

    this.store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAgencyNameListLoading = isLoading;
      });

    this.store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.channelPartnerList = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isChannelPartnerListLoading = isLoading;
      });

    this.store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.campaigns = item
          ?.filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCampaignListLoading = isLoading;
      });

    this.store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          ?.map(({ id, ...location }: any) => ({ locationId: id, ...location }))
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.addLeadForm.controls['leadSource']?.valueChanges.subscribe(
      (val: any) => {
        this.addLeadForm.patchValue({ subSource: null });
        this.updateSubSource(val);
      }
    );

    this.addLeadForm.valueChanges.subscribe((data: any) => {
      this.currFormValues = data;
    });
    this.addLeadForm.controls['contactNo'].valueChanges.subscribe(
      (val: string = '') => {
        if (this.selectedLeadId && val !== this.selectedLeadInfo?.contactNo) {
          this.numberEdited = true;
        } else if (this.selectedLeadId) {
          this.numberEdited = false;
        }

        if (
          val?.length &&
          this.contactNoInput &&
          val !== this.selectedLeadInfo?.contactNo
        ) {
          const input = document.querySelector(
            '.contactNoInput > div > input'
          ) as HTMLInputElement;
          if (!this.contactNoInput?.phoneNumber) {
            return
          }
          this.store.dispatch(
            new HasLeadInfo({
              value: val,
              number: this.contactNoInput?.phoneNumber,
              countryCode:
                '+' + this.getSelectedCountryCodeContactNo()?.dialCode,
            })
          );
          this.store.dispatch(new LoaderHide());
          this.store
            .select((state) => state.lead)
            .pipe(takeUntil(this.stopper))
            .subscribe((leadData: any) => {
              const isLoading = leadData?.isLoadingHasLeadInfo;
              const data = leadData?.data;
              this.duplicateLead = data;
              const isValidLead =
                data?.id && data.id !== this.selectedLeadId && !isLoading;
              const isContactChanged =
                this.addLeadForm.controls['contactNo'].value !==
                this.selectedLeadInfo?.contactNo;
              if (isValidLead && isContactChanged) {
                this.isduplicateLead = true;

                if (this.isCreateDuplicateLeads && this.isAllowDuplicate) {
                  this.checkDuplicacy = false;
                } else {
                  this.checkDuplicacy = true;
                  this.numberEdited =
                    !!data?.id && data.id !== this.selectedLeadId;
                }
              } else {
                this.isduplicateLead = false;
                this.checkDuplicacy = false;
              }
            });
        } else {
          this.isduplicateLead = false;
          this.checkDuplicacy = false;
        }
      }
    );
    this.addLeadForm.controls['alternateContactNo'].valueChanges.subscribe(
      (val: string) => {
        if (
          this.selectedLeadId &&
          val !== this.selectedLeadInfo?.alternateContactNo
        ) {
          this.alternateNumberEdited = true;
        } else if (this.selectedLeadId) {
          this.alternateNumberEdited = false;
        }
        if (val?.length && this.alternateNoInput && val !== this.selectedLeadInfo?.alternateContactNo) {
          const input = document.querySelector(
            '.alternateNoInput > div > input'
          ) as HTMLInputElement;
          if (!this.alternateNoInput?.phoneNumber) {
            return
          }
          this.store.dispatch(
            new HasLeadAltInfo({
              value: val,
              number: +input?.value?.replace(' ', '') || '',
              countryCode:
                '+' + this.getSelectedCountryCodeAlternateNo()?.dialCode,
            })
          );
          this.store.dispatch(new LoaderHide());
          this.store
            .select((state) => state.lead)
            .pipe(takeUntil(this.stopper))
            .subscribe((leadData: any) => {
              const isLoading = leadData?.isLoadingHasLeadAltInfo;
              const data = leadData?.altData;
              this.duplicateAltLead = data;
              if (data?.id && data?.id !== this.selectedLeadId && !isLoading) {
                this.isduplicateLead = true;
              } else {
                this.isduplicateLead = false;
              }
              if (this.isCreateDuplicateLeads && this.isAllowDuplicate) {
                this.checkAlternateNumDuplicacy = false;
              } else if (
                data?.id &&
                data?.id !== this.selectedLeadId &&
                !isLoading
              ) {
                this.checkAlternateNumDuplicacy = true;
                if (data?.id)
                  this.alternateNumberEdited = data?.id !== this.selectedLeadId;
              } else {
                this.checkAlternateNumDuplicacy = false;
                this.isduplicateLead = false;
              }
            });
        } else {
          this.isduplicateLead = false;

          this.checkAlternateNumDuplicacy = false;
        }
      }
    );

    this.addLeadForm.get('currency').valueChanges.subscribe((value) => {
      if (!this.syncingCurrency) {
        this.syncingCurrency = true;
        this.addLeadForm.get('currency').setValue(value);
        this.syncingCurrency = false;
      }
    });

    this.addLeadForm
      .get('propertyTypeId')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(VALIDATION_SET, this.addLeadForm, 'propSubType', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.addLeadForm, 'propSubType');
        }
        this.addLeadForm.patchValue({
          propSubType: null,
        });
      });

    this.addLeadForm.get('carpetArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(VALIDATION_SET, this.addLeadForm, 'carpetAreaUnitId', [
          Validators.required,
        ]);
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'carpetAreaUnitId'
        );
        this.addLeadForm.patchValue({
          carpetAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addLeadForm
      .get('carpetAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.carpetAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addLeadForm.get('builtUpArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'builtUpAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'builtUpAreaUnitId'
        );
        this.addLeadForm.patchValue({
          builtUpAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addLeadForm
      .get('builtUpAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.builtUpAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });

    this.addLeadForm.get('saleableArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'saleableAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'saleableAreaUnitId'
        );
        this.addLeadForm.patchValue({
          saleableAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addLeadForm
      .get('saleableAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.saleableAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addLeadForm.get('propertyArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'propertyAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'propertyAreaUnitId'
        );
        this.addLeadForm.patchValue({
          propertyAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addLeadForm
      .get('propertyAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.propertyAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addLeadForm.get('netArea').valueChanges.subscribe((val: any) => {
      if (val) {
        toggleValidation(
          VALIDATION_SET,
          this.addLeadForm,
          'netAreaUnitId',
          [Validators.required]
        );
      } else {
        toggleValidation(
          VALIDATION_CLEAR,
          this.addLeadForm,
          'netAreaUnitId'
        );
        this.addLeadForm.patchValue({
          netAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.addLeadForm
      .get('netAreaUnitId')
      .valueChanges.subscribe((val: any) => {
        this.areaSizeUnits.filter((areaUnitId: any) => {
          if (areaUnitId.id == val) {
            this.netAreaConversion = areaUnitId.conversionFactor;
          }
        });
      });
    this.addLeadForm.get('currency').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        this.selectedLeadInfo?.enquiry?.lowerBudget ||
        this.addLeadForm.value.lowerBudget,
        val
      );
      this.upperBudgetInWords = formatBudget(
        this.selectedLeadInfo?.enquiry?.upperBudget ||
        this.addLeadForm.value.upperBudget,
        val
      );
    });

    this.addLeadForm.get('lowerBudget').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        val,
        this.addLeadForm.value.currency ||
        this.selectedLeadInfo?.enquiry?.currency ||
        this.defaultCurrency
      );
      if (!this.addLeadForm.value.upperBudget) {
        this.addLeadForm.value.upperBudget = val;
      } else {
        val > this.addLeadForm.get('upperBudget').value
          ? (this.budgetValidation = true)
          : (this.budgetValidation = false);
      }
      if (!val) {
        this.budgetValidation = false;
      }
    });

    this.addLeadForm.get('upperBudget').valueChanges.subscribe((val) => {
      this.upperBudgetInWords = formatBudget(
        val,
        this.addLeadForm.value.currency ||
        this.selectedLeadInfo?.enquiry?.currency ||
        this.defaultCurrency
      );
      this.addLeadForm.get('lowerBudget').value > val
        ? (this.budgetValidation = true)
        : (this.budgetValidation = false);
      if (!val) {
        this.budgetValidation = false;
      }
    });
    this.onUnitChange('carpetAreaUnitId');

    this.store
      .select(getInternationalLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.internationalLocations = data
          ?.slice()
          // ?.map(({ id, ...location }: any) => ({ locationId: id, ...location }))
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.store
      .select(getInternationLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isInternationalLocationLoading = isLoading;
      });

    this.store
      .select(getLocationsWithGoogleApiIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPlacesListLoading = isLoading;
      });

    const adminsWithReportees$ = this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));

    this.store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAdminAndReporteesLoading = isLoading;
      });

    const allUsers$ = this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));

    this.store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
      });

    this.store
      .select(getActiveLeadIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.activeLeadIsLoading = isLoading;
      });

    const permissions$ = this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));

    combineLatest({
      adminsWithReportees: adminsWithReportees$,
      allUsers: allUsers$,
      permissions: permissions$,
    }).subscribe(({ adminsWithReportees, allUsers, permissions }) => {
      this.allUsers = allUsers?.map((user: any) => {
        user = {
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        };
        return user;
      });
      if (permissions?.includes('Permissions.Leads.CreateDuplicateLeads')) {
        this.isCreateDuplicateLeads = true;
      }
      if (
        permissions?.includes('Permissions.Leads.Create') &&
        this.router.url?.includes('add')
      )
        this.canViewComponent = true;
      if (permissions?.includes('Permissions.Leads.Update'))
        this.canEdit = true;
      if (this.canEdit && this.router.url?.includes('edit'))
        this.canViewComponent = true;
      if (permissions?.includes('Permissions.Leads.Assign'))
        this.canAssign = true;
      if (permissions?.includes('Permissions.Leads.UpdateSource'))
        this.canAdminEditSource = true;
      if (permissions?.includes('Permissions.Leads.ViewLeadSource'))
        this.canViewLeadSource = true;
      if (permissions?.includes('Permissions.Leads.UpdateBasicInfo'))
        this.canUpdateInfo = true;

      if (permissions?.includes('Permissions.Users.AssignToAny')) {
        this.users = allUsers;
      } else {
        this.users = adminsWithReportees;
      }

      this.activeUsers = this.users?.filter((user: any) => user.isActive);
      this.inactiveUsers = this.users?.filter((user: any) => !user.isActive);
      this.primaryUserList = assignToSort(
        this.activeUsers,
        this.selectedLeadInfo?.assignTo
      );
      this.primaryAgentList = assignToSort(
        this.activeUsers,
        this.selectedLeadInfo?.assignTo
      );
      this.secondaryUserList = assignToSort(
        this.activeUsers,
        this.selectedLeadInfo?.secondaryUserId
      );
      this.secondaryAgentList = assignToSort(
        this.activeUsers,
        this.selectedLeadInfo?.secondaryUserId
      );
      this.primaryAgentList = this.primaryUserList.filter(
        (el: any) => !this.selectedLeadInfo?.secondaryUserId?.includes(el?.id)
      );
      this.secondaryAgentList = this.secondaryUserList.filter(
        (el: any) => !this.selectedLeadInfo?.assignTo?.includes(el?.id)
      );
    });

    this.addLeadForm.get('assignTo').valueChanges.subscribe((val) => {
      this.secondaryAgentList = this.secondaryUserList.filter(
        (el: any) => !val?.includes(el?.id)
      );
    });

    this.addLeadForm.get('secondaryAssignTo').valueChanges.subscribe((val) => {
      this.primaryAgentList = this.primaryUserList.filter(
        (el: any) => !val?.includes(el?.id)
      );
    });

    this.store
      .select(getLeadSourceIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLeadSourceListLoading = isLoading;
      });
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );
      });
    this.store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this.store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.projectListIsLoading = isLoading;
      });
    this.store
      .select(getPropertyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this.store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.propertyListIsLoading = isLoading;
      });

    this.store
      .select(getActiveLead)
      .pipe(
        takeUntil(this.stopper),
        filter((leadData: any) => leadData.id === this.selectedLeadId)
      )
      .subscribe((leadData: any) => {
        if (this.activeLeadIsLoading) {
          this.selectedLeadInfo = {};
          return;
        }

        this.selectedLeadInfo = leadData || {};
        // if (this.selectedLeadId) {
        //   if (this.isGlobalSourceEdit !== undefined) {
        //     if (this.isGlobalSourceEdit) {
        //       if (this.canAdminEditSource) {
        //         this.canEditSource = true;
        //       } else if (this.selectedLeadInfo?.isSourceUpdated) {
        //         this.canEditSource = false;
        //       } else {
        //         this.canEditSource = true;
        //       }
        //     } else {
        //       this.canEditSource = false;
        //     }
        //   } else {
        //     this.canEditSource = true;
        //   }
        // } else {
        //   this.canEditSource = true;
        // }
        if (!this.selectedLeadId) {
          this.canEditSource = true;
        } else if (this.isGlobalSourceEdit === undefined) {
          this.canEditSource = true;
        } else if (this.isGlobalSourceEdit) {
          this.canEditSource = this.canAdminEditSource;
        } else {
          this.canEditSource = false;
        }
        if (!this.selectedLeadInfo?.alternateContactNo) {
          const input = document.querySelector(
            '.alternateNoInput > div > input'
          ) as HTMLInputElement;
          if (input) input.value = null;
        }
        this.addLeadForm.patchValue({
          ...this.selectedLeadInfo,
          contactNo: this.selectedLeadInfo?.contactNo
            ? this.selectedLeadInfo.contactNo.includes('+')
              ? this.selectedLeadInfo.contactNo?.replace(/\s/g, '')
              : '+91' + this.selectedLeadInfo.contactNo?.replace(/\s/g, '')
            : null,
          alternateContactNo: this.selectedLeadInfo?.alternateContactNo
            ? this.selectedLeadInfo.alternateContactNo.includes('+')
              ? this.selectedLeadInfo.alternateContactNo?.replace(/\s/g, '')
              : '+91' +
              this.selectedLeadInfo.alternateContactNo?.replace(/\s/g, '')
            : null,
          referralContactNo: this.selectedLeadInfo?.referralContactNo
            ? this.selectedLeadInfo.referralContactNo.includes('+')
              ? this.selectedLeadInfo.referralContactNo?.replace(/\s/g, '')
              : '+91' +
              this.selectedLeadInfo.referralContactNo?.replace(/\s/g, '')
            : null,
          ...(this.selectedLeadInfo?.enquiry?.isPrimary && {
            enquiredLocationId:
              getLocationDetailsByObj(
                this.selectedLeadInfo?.enquiry?.address
              ) || null,
            enquiredLocality:
              getLocalityDetailsByObj(
                this.selectedLeadInfo?.enquiry?.address
              ) || null,
            enquiredSubCommunity:
              this.selectedLeadInfo.enquiry?.address?.subCommunity,
            enquiredCommunity:
              this.selectedLeadInfo.enquiry?.address?.community,
            enquiredTowerName:
              this.selectedLeadInfo.enquiry?.address?.towerName,
            enquiredCity: this.selectedLeadInfo.enquiry?.address?.city,
            enquiredState: this.selectedLeadInfo.enquiry?.address?.state,
            enquiredCountry: this.selectedLeadInfo.enquiry?.address?.country,
            leadSource: LeadSource[this.selectedLeadInfo.enquiry?.leadSource],
            subSource: this.selectedLeadInfo.enquiry?.subSource,
            enquiredFor:
              this.selectedLeadInfo.enquiry?.enquiryTypes?.map(
                (enquiryFor: any) => EnquiryType[enquiryFor]
              ) || [],
            purpose:
              this.selectedLeadInfo.enquiry?.purpose
                ? this.selectedLeadInfo.enquiry?.purpose
                : null,
            propertyTypeId:
              this.selectedLeadInfo.enquiry?.propertyTypes?.[0]?.displayName,
            propSubType:
              this.selectedLeadInfo.enquiry?.propertyTypes?.map((item: any) => item?.childType?.displayName),
            noOfBHK: this.globalSettingsDetails?.isCustomLeadFormEnabled
              ? this.selectedLeadInfo.enquiry?.bhKs
              : this.selectedLeadInfo.enquiry?.bhKs?.map((bhk: any) =>
                bhk?.toString()
              ),
            bhkType:
              this.selectedLeadInfo.enquiry?.bhkTypes?.map(
                (type: any) => BHKType[type]
              ) || [],
            baths: this.selectedLeadInfo?.enquiry?.baths,
            beds: this.selectedLeadInfo?.enquiry?.beds,
            offeringType: this.selectedLeadInfo.enquiry?.offerType
              ? this.selectedLeadInfo.enquiry?.offerType
              : null,
            furnishStatus: this.selectedLeadInfo.enquiry?.furnished
              ? FurnishStatus[this.selectedLeadInfo.enquiry?.furnished]
              : null,
            preferredFloors: this.selectedLeadInfo.enquiry?.floors,
            projectsList: this.selectedLeadInfo.projects?.map(
              (project: any) => project.name
            ),
            propertiesList: this.selectedLeadInfo.properties?.map(
              (property: any) => property.title
            ),
            lowerBudget: this.selectedLeadInfo.enquiry?.lowerBudget || null,
            upperBudget: this.selectedLeadInfo.enquiry?.upperBudget || null,
            possessionDate:
              patchTimeZoneDate(
                this.selectedLeadInfo.enquiry?.possessionDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ) || null,
            currency:
              this.selectedLeadInfo.enquiry?.currency || this.defaultCurrency,
            carpetArea: this.selectedLeadInfo.enquiry?.carpetArea || null,
            carpetAreaUnitId:
              this.selectedLeadInfo.enquiry?.carpetAreaUnitId &&
                this.selectedLeadInfo.enquiry?.carpetArea
                ? this.selectedLeadInfo.enquiry?.carpetAreaUnitId
                : null,
            builtUpArea: this.selectedLeadInfo.enquiry?.builtUpArea || null,
            builtUpAreaUnitId:
              this.selectedLeadInfo.enquiry?.builtUpAreaUnitId &&
                this.selectedLeadInfo.enquiry?.builtUpArea
                ? this.selectedLeadInfo.enquiry?.builtUpAreaUnitId
                : null,

            saleableArea: this.selectedLeadInfo.enquiry?.saleableArea || null,
            saleableAreaUnitId:
              this.selectedLeadInfo.enquiry?.saleableAreaUnitId &&
                this.selectedLeadInfo.enquiry?.saleableArea
                ? this.selectedLeadInfo.enquiry?.saleableAreaUnitId
                : null,
            propertyArea: this.selectedLeadInfo.enquiry?.propertyArea || null,
            propertyAreaUnitId:
              this.selectedLeadInfo.enquiry?.propertyAreaUnitId &&
                this.selectedLeadInfo.enquiry?.propertyArea
                ? this.selectedLeadInfo.enquiry?.propertyAreaUnitId
                : null,
            netArea: this.selectedLeadInfo.enquiry?.netArea || null,
            netAreaUnitId:
              this.selectedLeadInfo.enquiry?.netAreaUnitId &&
                this.selectedLeadInfo.enquiry?.netArea
                ? this.selectedLeadInfo.enquiry?.netAreaUnitId
                : null,
            unitName: this.selectedLeadInfo?.enquiry?.unitName,
            clusterName: this.selectedLeadInfo?.enquiry?.clusterName,
            nationality: this.selectedLeadInfo?.nationality,
            assignTo:
              this.selectedLeadInfo?.assignTo !== EMPTY_GUID
                ? this.selectedLeadInfo.assignTo
                : null,
            secondaryAssignTo:
              this.selectedLeadInfo?.secondaryUserId !== EMPTY_GUID
                ? this.selectedLeadInfo.secondaryUserId
                : null,
            agencies: this.selectedLeadInfo?.agencies?.map(
              (agency: any) => agency?.name
            ),
            possessionRange:
              this.selectedLeadInfo.possesionType === 0
                ? null
                : PossessionType[this.selectedLeadInfo.possesionType],
            customPossessionDate: this.selectedLeadInfo.enquiry?.possessionDate || null,
            gender: this.selectedLeadInfo?.gender ? this.selectedLeadInfo?.gender : null,
            dateOfBirth: this.selectedLeadInfo?.dateOfBirth ? getTimeZoneDate(this.selectedLeadInfo.dateOfBirth, '00:00:00', 'ISO') : null,
            maritalStatus: this.selectedLeadInfo?.maritalStatus ? this.selectedLeadInfo?.maritalStatus : null,
          }),
        });

        this.addLeadForm.patchValue({
          closingManager:
            this.selectedLeadInfo?.closingManager !== EMPTY_GUID
              ? this.selectedLeadInfo?.closingManager
              : null,
          sourcingManager:
            this.selectedLeadInfo?.sourcingManager !== EMPTY_GUID
              ? this.selectedLeadInfo?.sourcingManager
              : null,
          profession: this.selectedLeadInfo?.profession
            ? Profession[this.selectedLeadInfo?.profession]
            : null,
          customerLocationId:
            getLocationDetailsByObj(this.selectedLeadInfo?.address) || null,
          customerLocality:
            getLocalityDetailsByObj(this.selectedLeadInfo?.address) || null,
          customerSubCommunity: this.selectedLeadInfo.address?.subCommunity,
          customerCommunity: this.selectedLeadInfo?.address?.community,
          customerTowerName: this.selectedLeadInfo?.address?.towerName,
          customerCity: this.selectedLeadInfo.address?.city,
          customerState: this.selectedLeadInfo.address?.state,
          customerCountry: this.selectedLeadInfo.address?.country,
          customerPincode: this.selectedLeadInfo.address?.postalCode,
          channelPartnerList: this.selectedLeadInfo.channelPartners?.map(
            (channelPartner: any) => channelPartner.firmName
          ),
          campaigns: this.selectedLeadInfo?.campaigns?.map(
            (campaigns: any) => campaigns.name
          ),

          address: this.selectedLeadInfo?.address,
        });
        this.manualLocationsList =
          this.selectedLeadInfo?.enquiry?.addresses?.map((address: any) => {
            let adr = {
              ...address,
              locationId: address?.locationId ?? address?.locationId,
              placeId: address?.placeId ?? address?.placeId,
              locality: address?.subLocality || address?.locality,
              id: address?.id ?? address?.id,
            };
            return adr;
          });

        this.backupLocation = this.selectedLeadInfo?.enquiry?.addresses;
        if (this.selectedLeadId) {
          this.numberEdited = false;
          this.alternateNumberEdited = false;
        }
      });

    this.store.dispatch(new FetchAllSources());
    this.store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
      });
    this.store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });
    if (this.globalSettingsDetails?.defaultValues?.masterAreaUnit) {
      const currentValues = this.addLeadForm.value;
      this.addLeadForm.patchValue({
        carpetAreaUnitId: currentValues.carpetAreaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        builtUpAreaUnitId: currentValues.builtUpAreaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        saleableAreaUnitId: currentValues.saleableAreaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        netAreaUnitId: currentValues.netAreaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        propertyAreaUnitId: currentValues.propertyAreaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      });
    }
  }

  scrollTo(sectionName: string, index: number): void {
    this.currentActive = index;
    this.scrollpause = true;
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
      targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setTimeout(() => {
        this.scrollpause = false;
      }, 1000);
    }
  }

  onScroll(event: Event): void {
    const container = event.target as HTMLElement;
    const containerTop = container.getBoundingClientRect().top;

    this.fieldSections.forEach((section: { name: string }, index: any) => {
      const sectionElement = document.getElementById(section.name);

      if (sectionElement && !this.scrollpause) {
        const sectionRect = sectionElement.getBoundingClientRect();
        const sectionTop = sectionRect.top - containerTop - 100;
        const sectionBottom = sectionRect.bottom - containerTop - 100;
        if (sectionTop <= 0 && sectionBottom > 0) {
          this.currentActive = index;
        }
      }
    });
  }

  customSearch(): boolean {
    return true;
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Backspace' || event.keyCode === 8) {
      this.isBackSpace = true;
    } else {
      this.isBackSpace = false;
    }
  }

  updateSubSource(selectedSource?: any) {
    const cleanKey = (key: string): string => key?.replace(/\s+/g, '');
    const sourceMap: { [key: string]: string } = {
      '99Acres': 'NinetyNineAcres',
      'GoogleSheets': 'GoogleSheet',
    };
    if (selectedSource) {
      const cleanedSource = cleanKey(selectedSource);
      const mappedKey = sourceMap[cleanedSource] || cleanedSource;
      this.subSources = this.subSourceList[mappedKey] || [];
    } else {
      const allSubSources: string[] = this.leadSources?.flatMap((lead: any) => {
        const cleanedKey = cleanKey(lead?.displayName || '');
        const mappedKey = sourceMap[cleanedKey] || cleanedKey;
        return this.subSourceList[mappedKey] || [];
      }) || [];
      this.subSources = allSubSources;
    }
  }

  getSelectedCountryCodeContactNo(): any {
    return this.contactNoInput?.selectedCountry;
  }

  getSelectedCountryCodeAlternateNo(): any {
    return this.alternateNoInput?.selectedCountry;
  }

  getSelectedCountryCodeReferralNo(): any {
    return this.referralNoInput?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const primaryNumber = this.addLeadForm?.get('contactNo')?.value;
      const alternativeNumber =
        this.addLeadForm?.get('alternateContactNo')?.value;
      if (numType == 'primary') {
        const input = document.querySelector(
          '.contactNoInput > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length && !control?.value) {
          return { required: true };
        }
        defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
        if (control.value && alternativeNumber) {
          if (control.value === alternativeNumber) {
            this.addLeadForm
              .get('alternateContactNo')
              ?.setErrors({ samePhoneNumber: true });
            this.addLeadForm.get('alternateContactNo')?.markAsTouched();
          } else {
            this.addLeadForm
              .get('alternateContactNo')
              ?.setErrors({ samePhoneNumber: false });
            this.addLeadForm.get('alternateContactNo')?.markAsPristine();
            this.addLeadForm.get('alternateContactNo')?.markAsUntouched();
          }
          this.addLeadForm.get('alternateContactNo')?.updateValueAndValidity();
          return null;
        }
      } else if (numType == 'alternative') {
        const input = document.querySelector(
          '.alternateNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeAlternateNo()?.dialCode;

        if (control.value && primaryNumber && control.value === primaryNumber) {
          return { samePhoneNumber: true };
        }
      } else if (numType == 'referral') {
        const input = document.querySelector(
          '.referralNoInput > div > input'
        ) as HTMLInputElement;
        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeReferralNo()?.dialCode;
      }

      try {
        let phoneValue = '';
        if (numType === 'primary') {
          phoneValue = this.contactNoInput?.value;
        } else if (numType === 'alternative') {
          phoneValue = this.alternateNoInput?.value;
        } else if (numType === 'referral') {
          phoneValue = this.referralNoInput?.value;
        }

        const validNumber = isPossiblePhoneNumber(
          phoneValue || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  removeLocation(type: any) {
    switch (type) {
      case 'location':
        this.addLeadForm.value.enquiredLocationId = null;
        this.addLeadForm.value.enquiredLocality = null;
        this.addLeadForm.value.enquiredSubCommunity = null;
        this.addLeadForm.value.enquiredCommunity = null;
        this.addLeadForm.value.enquiredTowerName = null;
        this.addLeadForm.value.enquiredCity = null;
        this.addLeadForm.value.enquiredState = null;
        this.addLeadForm.value.enquiredCountry = null;
        break;
      case 'changeLocation':
        ['enquiredLocality',
          'enquiredSubCommunity',
          'enquiredCommunity',
          'enquiredTowerName',
          'enquiredCity',
          'enquiredState',
          'enquiredCountry',
          'enquiredPincode'].forEach((field) => {
            this.addLeadForm.get(field)?.reset();
          });
        break;
      case 'changeLocality':
        this.addLeadForm.patchValue({
          enquiredLocationId: null,
        });
        break;
    }
  }

  clearManualLocation(location: any) {
    this.manualLocationsList = this.manualLocationsList.filter(
      (manualLocation: any) =>
        JSON.stringify(manualLocation) != JSON.stringify(location)
    );
    this.addLeadForm?.markAsDirty();
  }

  addMoreLocation() {
    const {
      enquiredLocality,
      enquiredSubCommunity,
      enquiredCommunity,
      enquiredTowerName,
      enquiredCity,
      enquiredState,
      enquiredCountry,
      enquiredPincode,
    }: any = this.addLeadForm.value;
    const isEmpty = [
      enquiredLocality,
      enquiredSubCommunity,
      enquiredCommunity,
      enquiredTowerName,
      enquiredCity,
      enquiredState,
      enquiredCountry,
      enquiredPincode,
    ].every((field) => !field?.trim());
    if (isEmpty) return;
    const isLocationDuplicate = this.manualLocationsList.some((location: any) =>
      location.locality === enquiredLocality?.trim() &&
      location.subCommunity === enquiredSubCommunity?.trim() &&
      location.community === enquiredCommunity?.trim() &&
      location.towerName === enquiredTowerName?.trim() &&
      location.city === enquiredCity?.trim() &&
      location.state === enquiredState?.trim() &&
      location.country === enquiredCountry?.trim() &&
      location.pincode === enquiredPincode?.trim()
    );
    if (isLocationDuplicate) return;
    this.manualLocationsList.push({
      locality: enquiredLocality?.trim(),
      subCommunity: enquiredSubCommunity?.trim(),
      community: enquiredCommunity?.trim(),
      towerName: enquiredTowerName?.trim(),
      city: enquiredCity?.trim(),
      state: enquiredState?.trim(),
      country: enquiredCountry?.trim(),
      pincode: enquiredPincode?.trim(),
    });
    this.removeLocation('changeLocation');
  }

  postData() {
    const leadData = this.addLeadForm.value;
    // if (containsOnlyEmojis(leadData.name.trim())) {
    //   this._notificationService.warn('Please Enter a valid Lead Name');
    //   return;
    // }
    if (
      this.addLeadForm.value.lowerBudget &&
      !this.addLeadForm.value.upperBudget
    ) {
      this.addLeadForm.value.upperBudget = this.addLeadForm.value.lowerBudget;
    } else if (
      !this.addLeadForm.value.lowerBudget &&
      this.addLeadForm.value.upperBudget
    ) {
      this.addLeadForm.value.lowerBudget = this.addLeadForm.value.upperBudget;
    }

    if (
      this.addLeadForm.value.possessionRange === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossDate = true;
      this.isOpenPossessionModal = true;
      return;
    }

    if (
      !this.addLeadForm.valid ||
      (this.checkDuplicacy && this.numberEdited) ||
      (this.checkAlternateNumDuplicacy && this.alternateNumberEdited) ||
      this.addLeadForm.value.lowerBudget > this.addLeadForm.value.upperBudget
    ) {
      validateAllFormFields(this.addLeadForm);
      return;
    }
    if (!leadData.customerLocationId && !this.isShowManualCustomerLocation) {
      leadData.customerLocality = null;
      leadData.customerSubCommunity = null;
      leadData.customerCommunity = null;
      leadData.customerTowerName = null;
      leadData.customerCity = null;
      leadData.customerState = null;
      leadData.customerCountry = null;
    }
    let addLeadData = {
      ...(this.selectedLeadId && {
        ...this.selectedLeadInfo.leadTagInfo,
        ...this.selectedLeadInfo,
      }),
      ...leadData,
      dateOfBirth: leadData.dateOfBirth ? setTimeZoneDate(leadData.dateOfBirth, '00:00:00') : null,
      gender: leadData.gender ? leadData.gender : 0,
      maritalStatus: leadData.maritalStatus ? leadData.maritalStatus : 0,
      assignTo: leadData.assignTo
        ? getAssignedToDetails(leadData?.assignTo, this.users)?.id
        : null,
      secondaryUserId: leadData.secondaryAssignTo
        ? getAssignedToDetails(leadData?.secondaryAssignTo, this.users)?.id
        : null,
      name: leadData.name.trim(),
      contactNo: leadData.contactNo?.toString(),
      countryCode: leadData.contactNo ? this.getSelectedCountryCodeContactNo()?.dialCode : null,
      altCountryCode: leadData.alternateContactNo?.length ? this.getSelectedCountryCodeAlternateNo()?.dialCode : null,
      alternateContactNo: leadData.alternateContactNo?.length
        ? leadData.alternateContactNo?.toString()
        : null,
      referralContactNo: leadData.referralContactNo
        ? leadData.referralContactNo?.toString()
        : null,
      email: leadData.email,
      notes: leadData.notes,
      leadStatusId:
        this.selectedLeadInfo?.status?.childType?.id ||
        this.selectedLeadInfo?.status?.id,
      referralName: leadData.referralName,
      referralEmail: leadData.referralEmail,
      closingManager: leadData?.closingManager,
      sourcingManager: leadData?.sourcingManager,
      profession: leadData.profession ? Profession[leadData?.profession] : 0,
      // customer location
      address: {
        locationId:
          leadData.customerLocationId?.locationId ??
          leadData.customerLocationId?.locationId,
        placeId:
          leadData.customerLocationId?.placeId ??
          leadData.customerLocationId?.placeId,
        subLocality: leadData.customerLocality ?? leadData.customerLocality,
        subCommunity:
          leadData.customerSubCommunity ?? leadData.customerSubCommunity,
        community: leadData.customerCommunity ?? leadData.customerCommunity,
        towerName: leadData.customerTowerName ?? leadData.customerTowerName,
        city: leadData.customerCity ?? leadData.customerCity,
        state: leadData.customerState ?? leadData.customerState,
        country: leadData.customerCountry ?? leadData.customerCountry,
        postalCode: leadData.customerPincode ?? leadData.customerPincode,
      },
      channelPartnerList: leadData?.channelPartnerList,
      campaigns: leadData?.campaigns?.map((name: any) => ({ name })),
      companyName: leadData?.companyName,
      agencies: leadData?.agencies?.map((name: any) => ({ name })),
      designation: leadData?.designation,
      nationality: leadData?.nationality,
      landLine: leadData?.landLine?.toString(),
      possesionType: leadData?.possessionRange
        ? PossessionType[leadData?.possessionRange]
        : 0,
      enquiry: {
        bhkTypes:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType != 'Plot'
            ? leadData.bhkType?.map((bhkType: any) => BHKType[bhkType])
            : [],
        bhKs:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType !== 'Plot'
            ? (leadData?.noOfBHK || [])
              .filter((bhkNo: any) => bhkNo != null && parseFloat(bhkNo) > 0)
              ?.map((bhkNo: any) => parseFloat(bhkNo))
              .sort((a: number, b: number) => a - b)
            : [],
        beds:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType !== 'Plot'
            ? leadData?.beds
              ?.filter((bed: any) => bed != null)
              .slice()
              .sort((a: any, b: any) => String(a).localeCompare(String(b)))
            : [],
        baths:
          this.addLeadForm.get('propertyTypeId').value === 'Residential' &&
            leadData.propSubType !== 'Plot'
            ? leadData?.baths
              ?.filter((bed: any) => bed != null)
              .slice()
              .sort((a: any, b: any) => String(a).localeCompare(String(b)))
            : [],
        furnished: leadData.furnishStatus
          ? FurnishStatus[leadData.furnishStatus]
          : null,
        floors: leadData.preferredFloors ?? null,
        offerType: leadData.offeringType ?? null,
        enquiryTypes:
          leadData.enquiredFor?.map((enquiry: any) => EnquiryType[enquiry]) ||
          [],
        purpose: leadData.purpose ?? null,
        leadSource: LeadSource[leadData?.leadSource],
        subSource: leadData?.subSource,
        lowerBudget: leadData.lowerBudget ? leadData.lowerBudget : '0',
        upperBudget: leadData.upperBudget ? leadData.upperBudget : '0',
        currency: leadData.currency ? leadData.currency : this.defaultCurrency,
        carpetArea: leadData.carpetArea || 0,
        carpetAreaUnitId:
          leadData.carpetArea && leadData.carpetAreaUnitId
            ? leadData.carpetAreaUnitId
            : null,
        conversionFactor:
          leadData.carpetArea && leadData.carpetAreaUnitId
            ? Number(this.carpetAreaConversion)
            : null,
        builtUpArea: leadData.builtUpArea || 0,
        builtUpAreaUnitId:
          leadData.builtUpArea && leadData.builtUpAreaUnitId
            ? leadData.builtUpAreaUnitId
            : null,
        builtUpAreaConversionFactor:
          leadData.builtUpArea && leadData.builtUpAreaUnitId
            ? Number(this.builtUpAreaConversion)
            : null,
        saleableArea: leadData.saleableArea || 0,
        saleableAreaUnitId:
          leadData?.saleableArea && leadData.saleableAreaUnitId
            ? leadData.saleableAreaUnitId
            : null,
        saleableAreaConversionFactor:
          leadData?.saleableArea && leadData?.saleableAreaUnitId
            ? Number(this.saleableAreaConversion)
            : null,
        propertyArea: leadData.propertyArea || 0,
        propertyAreaUnitId:
          leadData?.propertyArea && leadData?.propertyAreaUnitId
            ? leadData.propertyAreaUnitId
            : null,
        propertyAreaConversionFactor:
          leadData?.propertyArea && leadData?.propertyAreaUnitId
            ? Number(this.propertyAreaConversion)
            : null,

        netArea: leadData?.netArea || 0,
        netAreaUnitId:
          leadData?.netArea && leadData?.netAreaUnitId
            ? leadData.netAreaUnitId
            : null,
        netAreaConversionFactor:
          leadData?.netArea && leadData?.netAreaUnitId
            ? Number(this.netAreaConversion)
            : null,
        unitName: leadData?.unitName,
        clusterName: leadData?.clusterName,
        propertyTypeIds: getPropertyTypeIds(
          this.propertyTypeList,
          leadData.propertyTypeId,
          leadData.propSubType
        ),

        addresses: (
          [
            ...(leadData?.enquiredLocationId || []),
            ...(this.manualLocationsList || []),
            ...(leadData.enquiredCity?.trim() ||
              leadData?.enquiredLocality?.trim() ||
              leadData?.enquiredSubCommunity?.trim() ||
              leadData?.enquiredCommunity?.trim() ||
              leadData?.enquiredTowerName?.trim() ||
              leadData?.enquiredState?.trim() ||
              leadData?.enquiredCountry?.trim()
              ? [
                {
                  enquiredLocality:
                    leadData.enquiredLocality ?? leadData.enquiredLocality,
                  enquiredSubCommunity:
                    leadData.enquiredSubCommunity ??
                    leadData.enquiredSubCommunity,
                  enquiredCommunity:
                    leadData.enquiredCommunity ?? leadData.enquiredCommunity,
                  enquiredTowerName:
                    leadData.enquiredTowerName ?? leadData.enquiredTowerName,
                  enquiredCity:
                    leadData.enquiredCity ?? leadData.enquiredCity,
                  enquiredState:
                    leadData.enquiredState ?? leadData.enquiredState,
                  enquiredCountry:
                    leadData.enquiredCountry ?? leadData.enquiredCountry,
                },
              ]
              : []),
          ] || this.backupLocation
        )?.map((location: any) => {
          let locationPayload = {
            locationId: location?.locationId ?? location?.locationId,
            placeId: location?.placeId ?? location?.placeId,
            subLocality:
              (location?.enquiredLocality ?? location?.enquiredLocality) ||
              (location?.locality ?? location?.locality),
            city:
              (location?.enquiredCity ?? location?.enquiredCity) ||
              (location?.city ?? location?.city),
            subCommunity:
              (location?.enquiredSubCommunity ??
                location?.enquiredSubCommunity) ||
              (location?.subCommunity ?? location?.subCommunity),
            community:
              (location?.enquiredCommunity ?? location?.enquiredCommunity) ||
              (location?.community ?? location?.community),
            towerName:
              (location?.enquiredTowerName ?? location?.enquiredTowerName) ||
              (location?.towerName ?? location?.towerName),
            state:
              (location?.enquiredState ?? location?.enquiredState) ||
              (location?.state ?? location?.state),
            country:
              (location?.enquiredCountry ?? location?.enquiredCountry) ||
              (location?.country ?? location?.country),
          };
          if (
            this.globalSettingsDetails?.isCustomLeadFormEnabled &&
            !location.placeId &&
            typeof location.location === 'string' &&
            location.location.trim()
          ) {
            const [
              towerName = '',
              subCommunity = '',
              community = '',
              city = '',
            ] = location.location.split(',').map((name: any) => name.trim());
            return {
              ...locationPayload,
              towerName,
              subCommunity,
              community,
              city,
            };
          }
          return locationPayload;
        }),
        possessionDate:
          leadData?.possessionRange === 'Under Construction'
            ? null
            : leadData?.possessionRange === 'Custom Date' ||
              leadData?.possessionRange === '6 Months' ||
              leadData?.possessionRange === '1 Year' ||
              leadData?.possessionRange === '2 Years'
              ? leadData?.customPossessionDate
                ? setTimeZoneDate(
                  leadData.customPossessionDate,
                  this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
                )
                : null
              : this.selectedLeadId && this.selectedLeadInfo?.enquiry?.possessionDate
                ? this.selectedLeadInfo?.enquiry?.possessionDate
                : null,
      },
    };
    if (
      this.globalSettingsDetails?.isCustomLeadFormEnabled &&
      !leadData.customerLocationId?.placeId
    ) {
      const location = leadData.customerLocationId?.location;

      if (location) {
        const [towerName, subCommunity, community, city] = location
          ?.split(',')
          ?.map((name: any) => name?.trim());
        addLeadData.address = {
          towerName: towerName ?? towerName,
          subCommunity: subCommunity ?? subCommunity,
          community: community ?? community,
          city: city ?? city,
        };
      }
    }
    if (this.selectedLeadId) {
      if (this.selectedLeadInfo.scheduledDate) {
        addLeadData.scheduledDate = this.selectedLeadInfo.scheduledDate;
      }
      this.isPostingData = true;
      this.store.dispatch(new UpdateLead(this.selectedLeadId, addLeadData));
      this.store
        .select(getLeadListIsPosting)
        .pipe(skipWhile((isLoading) => isLoading))
        .subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isPostingData = isLoading;
            this.goToManageLead();
          }
        });
    } else {
      this.isPostingData = true;
      this.store.dispatch(new AddLead(addLeadData));
      this.store
        .select(getLeadListIsPosting)
        .pipe(skipWhile((isLoading) => isLoading))
        .subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isPostingData = isLoading;
            this.goToManageLead();
          }
        });
    }
  }

  goToManageLead() {
    if (this.receivedCurrentPath === '/invoice') {
      return this.router.navigate(['/invoice']);
    } else {
      this.store.dispatch(new cancelAddLead(true));
      return this.router.navigate(['leads/manage-leads']);
    }
  }

  goToLead(shouldNavigateToAlternate: boolean = false) {
    if (this.canEdit) {
      this.checkDuplicacy = false;
      this.checkAlternateNumDuplicacy = false;
      location.href =
        'leads/edit-lead/' +
        (shouldNavigateToAlternate
          ? this.duplicateAltLead?.id
          : this.duplicateLead?.id);
    }
    return 0;
  }

  onUnitChange(unit: any) {
    const carpetAreaUnitId = this.addLeadForm.get(unit).value;
    this.addLeadForm.controls[unit]?.setValue(null);
    if (
      !this.addLeadForm.get('saleableAreaUnitId').value &&
      !this.addLeadForm.get('builtUpAreaUnitId').value &&
      !this.addLeadForm.get('carpetAreaUnitId').value &&
      !this.addLeadForm.get('propertyAreaUnitId').value &&
      !this.addLeadForm.get('netAreaUnitId').value
    ) {
      this.addLeadForm.controls['saleableAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addLeadForm.controls['propertyAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addLeadForm.controls['netAreaUnitId'].setValue(
        carpetAreaUnitId
      );
      this.addLeadForm.controls['builtUpAreaUnitId'].setValue(carpetAreaUnitId);
      this.addLeadForm.controls['carpetAreaUnitId'].setValue(carpetAreaUnitId);
    } else {
      this.addLeadForm.controls[unit].setValue(carpetAreaUnitId);
    }
  }
  confirmDuplicate() {
    if (
      this.addLeadForm.value.possessionRange === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossDate = true;
      this.isOpenPossessionModal = true;
      return;
    }

    if (
      !this.addLeadForm.valid ||
      (this.checkDuplicacy && this.numberEdited) ||
      (this.checkAlternateNumDuplicacy && this.alternateNumberEdited) ||
      this.addLeadForm.value.lowerBudget > this.addLeadForm.value.upperBudget
    ) {
      validateAllFormFields(this.addLeadForm);
      return;
    }
    const initialState: any = {
      type: 'leadDuplicate',
      data: {
        fieldType: 'Warning',
        heading: `Duplicate Lead Detected!`,
        message:
          'Lead with the same "Primary Number/Alternate Number" is already present in CRM.',
        module: 'lead',
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };

    const modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        { class: 'modal-400 top-modal ph-modal-unset', initialState }
      )
    );
    modalRef.content.navigateToParentLead.pipe(take(1)).subscribe(() => {
      this.goToLead();
    });
    modalRef.onHide.subscribe((reason: string) => {
      if (reason === 'confirmed') {
        this.postData();
      }
    });
  }
  monthChanged(event: Date): void {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.addLeadForm.controls['customPossessionDate'].setValue(
      this.selectedMonthAndYear
    );
    this.addLeadForm.controls['possessionDate'].setValue(
      this.selectedMonthAndYear
    );
    this.addLeadForm.markAsDirty();

    this.isValidPossDate = false;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.dt5.close();
    this.isOpenPossessionModal = false;
  }

  calculateDateRange(value: string): void {
    const currentDate = new Date(this.currentDate);
    let startDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      1
    );
    let endDate: Date;

    if (value === '6 Months') {
      endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);
    } else if (value === '1 Year') {
      endDate = new Date(startDate.getFullYear() + 1, startDate.getMonth(), 0);
    } else if (value === '2 Years') {
      endDate = new Date(startDate.getFullYear() + 2, startDate.getMonth(), 0);
    }

    this.addLeadForm.controls['customPossessionDate'].setValue(endDate);
  }

  convertDateToMonth(data: any) {
    if (!data) return;
    this.selectedMonthAndYear = data;
    this.selectedMonth = MONTHS[parseInt(data?.slice(5, 7), 10) - 1];
    this.selectedYear = parseInt(data?.slice(0, 4), 10);
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  closeModal() {
    if (
      this.addLeadForm.controls['possessionRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossDate = true;
      return;
    }
    this.isOpenPossessionModal = false;
  }

  handlePossessionRangeChange(value: string): void {
    this.addLeadForm.get('possessionRange')?.setValue(value);
    this.addLeadForm.markAsDirty();

    if (value === 'Custom Date') {
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.selectedPossession = 'Custom Date';
      this.addLeadForm.controls['customPossessionDate'].setValue(null);
      this.isOpenPossessionModal = true;
      this.isValidPossDate = false;
    } else if (
      value === '6 Months' ||
      value === '1 Year' ||
      value === '2 Years'
    ) {
      this.calculateDateRange(value);
      const endDate = this.addLeadForm.get('customPossessionDate').value;
      this.addLeadForm.controls['possessionDate'].setValue(endDate);
      this.addLeadForm.controls['possessionDate'].markAsTouched();
      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isValidPossDate = false;
      this.isOpenPossessionModal = false;
    } else if (value === 'Under Construction') {
      this.addLeadForm.controls['customPossessionDate'].setValue(null);
      this.addLeadForm.controls['possessionDate'].setValue(null);
      this.selectedPossession = value;
      this.isOpenPossessionModal = false;
    }
  }

  onPossessionChange(event: { possessionType: number | null; customDate: string | null; displayValue: string }): void {
    this.addLeadForm.patchValue({
      possessionType: event.possessionType,
      customPossessionDate: event.customDate
    });
  }

  openPreview(): void {
    this.modalRef = this.modalService.show(this.previewModal, {
      class: 'modal-600 modal-dialog-centered ip-modal-unset'
    });
  }

  ngOnDestroy() {
    this.store.dispatch(new removeLocationsWithGoogleApi());
    this.stopper.next();
    this.stopper.complete();
  }
}
