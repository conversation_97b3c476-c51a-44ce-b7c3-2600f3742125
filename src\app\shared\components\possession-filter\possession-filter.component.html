<div class="flex-column">
  <!-- Chip Layout for Possession Options -->
  <div class="possession-chips-container d-flex flex-wrap">
    <ng-container *ngFor="let type of dateFilterList; let i = index">
      <div class="possession-chip mr-8 mb-8"
           [ngClass]="{'selected': selectedPossession === type.value}">
        <input type="radio"
               [id]="'possession-' + i"
               name="possessionRange"
               [(ngModel)]="selectedPossession"
               [value]="type.value"
               [ngModelOptions]="{standalone: true}"
               class="possession-radio"
               (ngModelChange)="handlePossessionRangeChange($event)">
        <label [for]="'possession-' + i" class="possession-label">
          {{ type.value === 'Custom Date' ? 'Custom' : type.displayName }}
        </label>
      </div>
    </ng-container>
  </div>

  <!-- Custom Date Section -->
  <div *ngIf="selectedPossession === 'Custom Date'" class="custom-date-section mt-12">
    <div class="position-relative">
      <div class="w-100 bg-white br-4">
        <!-- Single Custom Date Picker -->
        <div class="dashboard-filter form-group mb-16"
          [ngClass]="{'border-red-30': !customDateValidation && isSearchClicked}">
          <div class="text-dark-gray text-large mb-8">Select Possession Date</div>
          <div class="position-relative field-rupees-tag" style="max-width: 250px;">
            <div class="rupees-sm icon ic-calendar ic-xxs ic-coal"></div>
            <input type="text"
                   readonly
                   [owlDateTimeTrigger]="dtCustomPossession"
                   [owlDateTime]="dtCustomPossession"
                   class="h-32"
                   placeholder="dd-mm-yyyy"
                   [value]="getFormattedCustomDate()"
                   (ngModelChange)="customPossessionDateChange($event)"
                   [ngModelOptions]="{standalone: true}"
                   [ngModel]="getFormValue(formControlNames.customDate)" />
            <owl-date-time [pickerType]="'calendar'"
                           #dtCustomPossession
                           [startAt]="currentDate"
                           [min]="currentDate">
            </owl-date-time>
          </div>
        </div>

        <!-- Date Validation Error -->
        <div *ngIf="selectedPossession === 'Custom Date' && !customDateValidation && isSearchClicked"
          class="text-xs text-red fw-semi-bold mb-10">
          Please select a valid possession date
        </div>
      </div>
    </div>
  </div>
</div>