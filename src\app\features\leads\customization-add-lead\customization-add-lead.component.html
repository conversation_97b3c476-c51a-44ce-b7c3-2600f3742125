<div (click)="goToManageLead()" [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<ng-container *ngIf="canViewComponent">
    <div class="px-30 py-20" [ngClass]="{'blinking pe-none': isPostingData}">
        <div class="d-flex mb-12">
            <div class="border br-20 bg-white align-center user">
                <ng-container *ngFor="let section of fieldSections; let i = index">
                    <div class="activation" [ngClass]="{ active: currentActive == i }"
                        (click)="scrollTo(section?.name, i); section.isHide = false">
                        <span class="icon ic-sm mr-8 ip-mr-4 ph-mr-0"
                            [ngClass]="[section?.icon, currentActive == i ? '' : 'ic-light-gray']"></span>
                        <span class="ph-d-none">{{ section?.name }}</span>
                    </div>
                </ng-container>
            </div>
        </div>
        <form [formGroup]="addLeadForm" autocomplete="off" class="scrollbar h-100-207" (scroll)="onScroll($event)">
            <div class="mr-12">
                <ng-container *ngFor="let section of fieldSections; let i = index; let last = last">
                    <div [ngClass]="{ 'pb-12': !last }" [id]="section?.name">
                        <div class="bg-white">
                            <div class="cursor-pointer align-center py-12 px-20"
                                [ngClass]="!section.isHide ? 'border-bottom' : ''"
                                (click)="section.isHide = !section.isHide">
                                <span class="ic-triangle-down icon ic-coal ic-xxxs mr-6"
                                    [ngClass]="{ 'rotate-270': section.isHide }"></span>
                                <h6 class="text-black-200">{{ section?.name }}</h6>
                            </div>
                            <div *ngIf="!section.isHide">
                                <ng-container [ngSwitch]="section?.name">
                                    <ng-container *ngSwitchCase="'Lead Info'">
                                        <div class="d-flex flex-wrap pl-20 pb-20 pr-8">
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label-req">{{ 'GLOBAL.name' | translate
                                                        }}</div>
                                                    <form-errors-wrapper [control]="addLeadForm.controls['name']"
                                                        label="{{ 'GLOBAL.name' | translate }}"
                                                        [ngClass]="{'non-editable' : !canUpdateInfo && !isAddLead }">
                                                        <input type="text" required formControlName="name"
                                                            id="inpLeadName" data-automate-id="inpLeadName"
                                                            placeholder="ex. Mounika Pampana" maxlength="75"
                                                            [readOnly]="!canUpdateInfo && !isAddLead"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}">
                                                        <p class="position-absolute right-4 bottom-0">{{
                                                            addLeadForm.controls['name']?.value?.length }}/75</p>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="position-relative mr-12">
                                                    <div class="field-label-req">{{'INTEGRATION.primary' | translate}}
                                                        {{'GLOBAL.number' | translate}}</div>
                                                    <form-errors-wrapper [control]="addLeadForm.controls['contactNo']"
                                                        label="Primary Number"
                                                        [ngClass]="{'non-editable pe-none' : !canUpdateInfo && !isAddLead }">
                                                        <ngx-mat-intl-tel-input #contactNoInput
                                                            *ngIf="hasInternationalSupport"
                                                            [preferredCountries]="preferredCountries"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [enablePlaceholder]="true" [enableSearch]="true"
                                                            formControlName="contactNo"
                                                            class="no-validation contactNoInput">
                                                        </ngx-mat-intl-tel-input>
                                                        <ngx-mat-intl-tel-input #contactNoInput
                                                            *ngIf="!hasInternationalSupport"
                                                            [preferredCountries]="preferredCountries"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [onlyCountries]="preferredCountries"
                                                            [enablePlaceholder]="true" [enableSearch]="true"
                                                            formControlName="contactNo"
                                                            class="no-validation contactNoInput">
                                                        </ngx-mat-intl-tel-input>
                                                    </form-errors-wrapper>
                                                    <div *ngIf="checkDuplicacy && numberEdited && addLeadForm?.controls?.['contactNo']?.status === 'VALID'"
                                                        class="mt-4 text-xs text-red position-absolute right-0 fw-semi-bold z-index-1021">
                                                        {{ 'CONTACT.already-exist' | translate }}
                                                        <span *ngIf="duplicateLead?.canNavigate" (click)="goToLead()"
                                                            class="cursor-pointer text-decoration-underline text-sm">
                                                            {{ 'CONTACT.check' | translate }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="position-relative mr-12">
                                                    <div class="field-label">{{'GLOBAL.alternate' | translate}}
                                                        {{'GLOBAL.number' | translate}}</div>
                                                    <form-errors-wrapper
                                                        [control]="addLeadForm.controls['alternateContactNo']"
                                                        label="{{'GLOBAL.alternate' | translate}} {{'GLOBAL.number' | translate}}"
                                                        [ngClass]="{'non-editable pe-none' : !canUpdateInfo && !isAddLead }">
                                                        <ngx-mat-intl-tel-input #alternateNoInput
                                                            *ngIf="hasInternationalSupport"
                                                            [preferredCountries]="preferredCountries"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [enablePlaceholder]="true" [enableSearch]="true"
                                                            formControlName="alternateContactNo"
                                                            class="alternateNoInput no-validation">
                                                        </ngx-mat-intl-tel-input>
                                                        <ngx-mat-intl-tel-input #alternateNoInput
                                                            *ngIf="!hasInternationalSupport"
                                                            [preferredCountries]="preferredCountries"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [onlyCountries]="preferredCountries"
                                                            [enablePlaceholder]="true" [enableSearch]="true"
                                                            formControlName="alternateContactNo"
                                                            class="alternateNoInput no-validation">
                                                        </ngx-mat-intl-tel-input>
                                                    </form-errors-wrapper>
                                                    <div *ngIf="checkAlternateNumDuplicacy && alternateNumberEdited && addLeadForm?.controls?.['alternateContactNo']?.status === 'VALID' && addLeadForm?.controls?.['alternateContactNo']?.value"
                                                        class="mt-4 text-xs text-red position-absolute right-0 fw-semi-bold">
                                                        {{ 'CONTACT.already-exist' | translate }}
                                                        <span *ngIf="duplicateAltLead?.canNavigate"
                                                            (click)="goToLead(duplicateAltLead?.canNavigate)"
                                                            class="cursor-pointer text-decoration-underline text-sm">
                                                            {{ 'CONTACT.check' | translate }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">Landline Number</div>
                                                    <form-errors-wrapper [control]="addLeadForm.controls['landLine']"
                                                        label="Landline Number">
                                                        <input type="text"  (keypress)="allowLandlineInput($event)" formControlName="landLine" id="landLine"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            data-automate-id="landLine" placeholder="ex. 022-25846975">
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'USER.email' |
                                                        translate}}</div>
                                                    <form-errors-wrapper
                                                        [ngClass]="{'non-editable' : !canUpdateInfo && !isAddLead }"
                                                        [control]="addLeadForm.controls['email']"
                                                        label="{{'USER.email' | translate}}">
                                                        <input type="email" formControlName="email" id="inpLeadMail"
                                                            data-automate-id="inpLeadMail"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            placeholder="ex. <EMAIL>"
                                                            [readOnly]="!canUpdateInfo && !isAddLead">
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEAD_FORM.referral-name' |
                                                        translate}}</div>
                                                    <div class="form-group">
                                                        <input type="text" formControlName="referralName"
                                                            id="inpReferralName"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            data-automate-id="inpReferralName" placeholder="ex. Manasa">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label text-nowrap">{{'LEAD_FORM.referral-phone-no'
                                                        |
                                                        translate}}</div>
                                                    <form-errors-wrapper
                                                        [control]="addLeadForm.controls['referralContactNo']"
                                                        label="{{'LEAD_FORM.referral-phone-no' | translate}}">
                                                        <ngx-mat-intl-tel-input #referralNoInput
                                                            *ngIf="hasInternationalSupport"
                                                            [preferredCountries]="preferredCountries"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [enablePlaceholder]="true" [enableSearch]="true"
                                                            formControlName="referralContactNo"
                                                            class="no-validation referralNoInput">
                                                        </ngx-mat-intl-tel-input>
                                                        <ngx-mat-intl-tel-input #referralNoInput
                                                            *ngIf="!hasInternationalSupport"
                                                            [preferredCountries]="preferredCountries"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [onlyCountries]="preferredCountries"
                                                            [enablePlaceholder]="true" [enableSearch]="true"
                                                            formControlName="referralContactNo"
                                                            class="no-validation referralNoInput">
                                                        </ngx-mat-intl-tel-input>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">Referral {{'USER.email' |
                                                        translate}}</div>
                                                    <form-errors-wrapper
                                                        [ngClass]="{'non-editable' : !canUpdateInfo && !isAddLead }"
                                                        [control]="addLeadForm.controls['referralEmail']"
                                                        label="Referral {{'USER.email' | translate}}">
                                                        <input type="email" formControlName="referralEmail"
                                                            id="inpReferralMail" data-automate-id="inpReferralMail"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            placeholder="ex. <EMAIL>"
                                                            [readOnly]="!canUpdateInfo && !isAddLead">
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="canViewLeadSource">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">{{'LEADS.source' |
                                                            translate}}</div>
                                                        <ng-select [virtualScroll]="true" formControlName="leadSource"
                                                            [readonly]="canEditSource ? false : true" ResizableDropdown
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isLeadSourceListLoading || isSourcesLoading}"
                                                            placeholder="{{ 'GLOBAL.select' | translate }} {{ 'LEADS.source' | translate }}">
                                                            <ng-option *ngFor="let source of leadSources"
                                                                [value]="source.displayName" class="w-100"
                                                                [disabled]="!source.isEnabled"
                                                                [ngClass]="{'pe-none': !source.isEnabled}">
                                                                <div class="dropdown-position">
                                                                    <span
                                                                        class="text-truncate-1 break-all">{{source.displayName}}</span>
                                                                    <span class="text-disabled"
                                                                        *ngIf="!source.isEnabled"> (Disabled)</span>
                                                                </div>
                                                            </ng-option>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">{{'LEADS.sub-source' |
                                                            translate}}</div>
                                                        <ng-select [virtualScroll]="true" formControlName="subSource"
                                                            [readonly]="canEditSource ? false : true" ResizableDropdown
                                                            [items]="subSources"
                                                            [ngClass]="{'blinking pe-none': isSourcesLoading}"
                                                            [addTag]="true" addTagText="Create New sub-source"
                                                            placeholder="Select/Create">
                                                            <ng-template ng-option-tmp let-item="item">
                                                                <div [title]="item">{{item}}</div>
                                                            </ng-template>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div
                                                        [ngClass]="addLeadForm.controls['secondaryAssignTo'].value ? 'field-label-req' : 'field-label'">
                                                        {{isDualOwnershipEnabled ? 'Primary Owner': 'Assign to'}}</div>
                                                    <form-errors-wrapper [control]="addLeadForm.controls['assignTo']"
                                                        label="Primary Owner">
                                                        <ng-select [virtualScroll]="true" formControlName="assignTo"
                                                            ResizableDropdown
                                                            [required]="addLeadForm.controls['secondaryAssignTo'].value ? true : false"
                                                            [ngClass]="{'disabled' : !canAssign, 'blinking pe-none': (!isAddLead && ((!isSelectedLeadInfoFetched || activeLeadIsLoading))) || isAdminAndReporteesLoading || isAllUsersLoading}"
                                                            class="manage-dropdown" placeholder="ex. Manasa">
                                                            <ng-option *ngFor="let user of primaryAgentList"
                                                                [value]="user.id">
                                                                {{user.firstName}} {{user.lastName}} <span
                                                                    class="d-none">{{user.fullName}}</span></ng-option>
                                                            <ng-option *ngFor="let user of inactiveUsers"
                                                                [value]="user.id" [disabled]="true">
                                                                <div class="dropdown-position"> <span
                                                                        class="text-truncate-1 break-all"> {{
                                                                        user.firstName
                                                                        }} {{
                                                                        user.lastName }}</span> <span
                                                                        class="d-none">{{user.fullName}}</span><span
                                                                        class="text-disabled" *ngIf="!user.isActive">
                                                                        (Disabled)</span></div>
                                                            </ng-option>
                                                        </ng-select>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100"
                                                *ngIf="isDualOwnershipEnabled">
                                                <div class="mr-12">
                                                    <div class="field-label">Secondary Owner</div>
                                                    <ng-select [virtualScroll]="true" ResizableDropdown
                                                        [readonly]="!addLeadForm.controls['assignTo'].value && !addLeadForm.controls['secondaryAssignTo'].value ? true : false"
                                                        [ngClass]="{'disabled' : !canAssign, 'blinking pe-none': (!isAddLead && ((!isSelectedLeadInfoFetched || activeLeadIsLoading))) || isAdminAndReporteesLoading || isAllUsersLoading}"
                                                        formControlName="secondaryAssignTo" class="manage-dropdown"
                                                        placeholder="ex. Karthik">
                                                        <ng-option *ngFor="let user of secondaryAgentList"
                                                            [value]="user.id">
                                                            {{user.firstName}} {{user.lastName}} <span
                                                                class="d-none">{{user.fullName}}</span></ng-option>
                                                        <ng-option *ngFor="let user of inactiveUsers" [value]="user.id"
                                                            [disabled]="true">
                                                            <div class="dropdown-position"><span
                                                                    class="text-truncate-1 break-all">{{user.firstName}}
                                                                    {{user.lastName}}</span>
                                                                <span class="d-none">{{user.fullName}}</span><span
                                                                    class="text-disabled" *ngIf="!user.isActive">
                                                                    (Disabled)</span>
                                                            </div>
                                                        </ng-option>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <ng-container
                                                *ngIf="isShowManualCustomerLocation == false else manualCustomerLocation">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="justify-between align-end">
                                                            <div class="field-label text-nowrap mr-8">
                                                                {{'LEADS.customer-location' | translate}}</div>
                                                            <div class="cursor-pointer align-center fw-semi-bold text-accent-green text-sm mr-2 mb-4"
                                                                (click)="isShowManualCustomerLocation = !isShowManualCustomerLocation">
                                                                <span
                                                                    class="icon ic-xxxs ic-accent-green mr-4 ic-add"></span>
                                                                <span
                                                                    class="text-truncate-1 text-nowrap break-all">Manually
                                                                    Enter Location</span>
                                                            </div>
                                                        </div>
                                                        <ng-container
                                                            *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled else googleLocation">
                                                            <div class="field-tag">
                                                                <ng-select formControlName="customerLocationId"
                                                                    placeholder="Search Location"
                                                                    (search)="searchPlaceTerm$.next($event.term)"
                                                                    [searchFn]="customSearch"
                                                                    [editableSearchTerm]="true" [closeOnSelect]="false"
                                                                    [clearSearchOnAdd]="true" [virtualScroll]="true"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                    class="bg-white" (keydown)="onKeydown($event)"
                                                                    ResizableDropdown>
                                                                    <ng-option
                                                                        *ngFor="let item of internationalLocations"
                                                                        [value]="item">
                                                                        {{ getFormattedLocation(item?.location) }}
                                                                    </ng-option>
                                                                </ng-select>
                                                                <div class="position-absolute top-5 right-40"
                                                                    *ngIf="isInternationalLocationLoading">
                                                                    <img src="assets/images/loader-rat.svg"
                                                                        class="rat-loader h-30px w-30px" alt="loader" />
                                                                </div>
                                                                <div class="search icon ic-search ic-sm ic-coal"></div>
                                                            </div>
                                                        </ng-container>
                                                        <ng-template #googleLocation>
                                                            <div class="field-tag">
                                                                <ng-select formControlName="customerLocationId"
                                                                    [items]="placesList" bindLabel="location"
                                                                    placeholder="Search Location"
                                                                    (search)="searchPlaceTerm$.next($event.term)"
                                                                    [searchFn]="customSearch"
                                                                    [editableSearchTerm]="true" [closeOnSelect]="false"
                                                                    [clearSearchOnAdd]="true" [virtualScroll]="true"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                    class="bg-white" (keydown)="onKeydown($event)"
                                                                    ResizableDropdown>
                                                                    <ng-template ng-option-tmp let-item="item"
                                                                        let-item$="item$" let-index="index">
                                                                        <div [title]="item?.location">{{item?.location}}
                                                                        </div>
                                                                    </ng-template>
                                                                </ng-select>
                                                                <div class="position-absolute top-5 right-40"
                                                                    *ngIf="isPlacesListLoading">
                                                                    <img src="assets/images/loader-rat.svg"
                                                                        class="rat-loader h-30px w-30px" alt="loader">
                                                                </div>
                                                                <div class="search icon ic-search ic-sm ic-coal"></div>
                                                            </div>
                                                        </ng-template>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-template #manualCustomerLocation>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="justify-between align-end">
                                                            <div class="field-label">Customer Locality</div>
                                                            <div class="cursor-pointer align-center fw-semi-bold text-accent-green text-sm mr-2 mb-4"
                                                                (click)="isShowManualCustomerLocation = !isShowManualCustomerLocation">
                                                                <span
                                                                    class="icon ic-xxxs ic-accent-green mr-4 ic-search"></span>
                                                                <span
                                                                    class="text-truncate-1 text-nowrap break-all">Location
                                                                    List</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="customerLocality"
                                                                placeholder="enter locality">
                                                        </div>
                                                    </div>
                                                </div>
                                                <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Customer Sub-Community</div>
                                                            <div class="form-group">
                                                                <input type="text"
                                                                    formControlName="customerSubCommunity"
                                                                    placeholder="enter sub-community">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Customer Community</div>
                                                            <div class="form-group">
                                                                <input type="text" formControlName="customerCommunity"
                                                                    placeholder="enter community">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Customer Tower Name</div>
                                                            <div class="form-group">
                                                                <input type="text" formControlName="customerTowerName"
                                                                    placeholder="enter tower name">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Customer City</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="customerCity"
                                                                placeholder="enter city">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Customer State</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="customerState"
                                                                placeholder="enter state">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Customer Country</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="customerCountry"
                                                                placeholder="enter country">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <label class="field-label">Customer Pincode</label>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="customerPincode"
                                                                placeholder="enter pincode">
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </div>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'Enquiry Info'">
                                        <div class="d-flex flex-wrap pl-20 pb-20 pr-8">

                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="field-rupees-tag mr-12">
                                                    <div class="field-label">Min. {{'LABEL.budget' |
                                                        translate}}</div>
                                                    <div class="position-relative budget-dropdown">
                                                        <form-errors-wrapper
                                                            [control]="addLeadForm.controls['lowerBudget']"
                                                            label="Min. {{'LABEL.budget' | translate}}">
                                                            <input type="number" formControlName="lowerBudget" min="1"
                                                                id="inpLeadLowerBudget"
                                                                [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                data-automate-id="inpLeadLowerBudget"
                                                                placeholder="ex. 4000000" maxlength="10"
                                                                (keydown)="onlyNumbers($event)">
                                                            <div class="no-validation">
                                                                <ng-container
                                                                    *ngIf="currencyList?.length > 1 ; else showCurrencySymbol">
                                                                    <ng-select formControlName="currency"
                                                                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown"
                                                                        ResizableDropdown>
                                                                        <ng-option *ngFor="let curr of currencyList"
                                                                            [value]="curr.currency">
                                                                            <span
                                                                                [title]="curr.currency">{{curr.currency}}</span>
                                                                        </ng-option>
                                                                    </ng-select>
                                                                </ng-container>
                                                                <ng-template #showCurrencySymbol>
                                                                    <h5 class="rupees px-12 py-8 fw-600 m-4">{{
                                                                        selectedLeadInfo?.enquiry?.currency ||
                                                                        defaultCurrency }}</h5>
                                                                </ng-template>
                                                            </div>
                                                        </form-errors-wrapper>
                                                        <div *ngIf="addLeadForm.controls['lowerBudget'].value"
                                                            class="position-absolute right-5 bottom-1 text-accent-green fw-semi-bold text-xxs">
                                                            {{lowerBudgetInWords}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="field-rupees-tag mr-12">
                                                    <div class="field-label">Max. {{'LABEL.budget' |
                                                        translate}}</div>
                                                    <div class="position-relative budget-dropdown">
                                                        <form-errors-wrapper
                                                            [control]="addLeadForm.controls['upperBudget']"
                                                            label="Max. {{'LABEL.budget' | translate}}">
                                                            <input type="number" formControlName="upperBudget" min="1"
                                                                id="inpLeadUpperBudget"
                                                                [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                data-automate-id="inpLeadUpperBudget"
                                                                placeholder="ex. 4000000" maxlength="10"
                                                                (keydown)="onlyNumbers($event)">
                                                            <div class="no-validation">
                                                                <ng-container
                                                                    *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                                                    <ng-select formControlName="currency"
                                                                        class="ml-4 mt-4 position-absolute top-0 manage-dropdown"
                                                                        ResizableDropdown>
                                                                        <ng-option *ngFor="let curr of currencyList"
                                                                            [value]="curr.currency">
                                                                            <span
                                                                                [title]="curr.currency">{{curr.currency}}</span>
                                                                        </ng-option>
                                                                    </ng-select>
                                                                </ng-container>
                                                            </div>
                                                        </form-errors-wrapper>
                                                        <div *ngIf="addLeadForm.controls['upperBudget'].value"
                                                            class="position-absolute right-5 bottom-1 text-accent-green fw-semi-bold text-xxs">
                                                            {{upperBudgetInWords}}</div>
                                                        <div *ngIf="budgetValidation"
                                                            class="mt-4 text-xxs text-error-red position-absolute right-0 fw-semi-bold">
                                                            {{ 'LEADS.budget-validation' | translate }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div
                                                        [ngClass]="addLeadForm.controls['carpetArea'].value ? 'field-label-req' : 'field-label'">
                                                        {{'LEADS.carpet-area' | translate}}</div>
                                                    <div class="align-center">
                                                        <div class="w-50 mr-6">
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['carpetArea']"
                                                                label="{{'LEADS.carpet-area' | translate}}">
                                                                <input type="number" min="0" placeholder="ex. 1906"
                                                                    formControlName="carpetArea"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}">
                                                            </form-errors-wrapper>
                                                        </div>
                                                        <div class="w-50">
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['carpetAreaUnitId']"
                                                                label="{{'PROJECTS.carpet-area-unit' | translate}}">
                                                                <ng-select [virtualScroll]="true"
                                                                    formControlName="carpetAreaUnitId" ResizableDropdown
                                                                    placeholder="ex. sq. feet."
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAreaUnitLoading}"
                                                                    [readonly]="addLeadForm.controls['carpetArea']?.value ? false : true"
                                                                    bindValue="id" bindLabel="unit"
                                                                    [items]="areaSizeUnits"
                                                                    (change)="onUnitChange('carpetAreaUnitId')"></ng-select>
                                                            </form-errors-wrapper>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div
                                                        [ngClass]="addLeadForm.controls['builtUpArea'].value ? 'field-label-req' : 'field-label'">
                                                        Built-up Area</div>
                                                    <div class="align-center">
                                                        <div class="w-50 mr-6">
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['builtUpArea']"
                                                                label="Built-up Area">
                                                                <input type="number" min="0" placeholder="ex. 1906"
                                                                    formControlName="builtUpArea"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}">
                                                            </form-errors-wrapper>
                                                        </div>
                                                        <div class="w-50">
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['builtUpAreaUnitId']"
                                                                label="Built-up Area Unit">
                                                                <ng-select [virtualScroll]="true"
                                                                    formControlName="builtUpAreaUnitId"
                                                                    ResizableDropdown placeholder="ex. sq. feet."
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAreaUnitLoading}"
                                                                    [readonly]="addLeadForm.controls['builtUpArea']?.value ? false : true"
                                                                    bindValue="id" bindLabel="unit"
                                                                    [items]="areaSizeUnits"
                                                                    (change)="onUnitChange('builtUpAreaUnitId')"></ng-select>
                                                            </form-errors-wrapper>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div
                                                        [ngClass]="addLeadForm.controls['saleableArea'].value ? 'field-label-req' : 'field-label'">
                                                        Saleable Area</div>
                                                    <div class="align-center">
                                                        <div class="w-50 mr-6">
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['saleableArea']"
                                                                label="Saleable Area">
                                                                <input type="number" min="0" placeholder="ex. 1906"
                                                                    formControlName="saleableArea"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}">
                                                            </form-errors-wrapper>
                                                        </div>
                                                        <div class="w-50">
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['saleableAreaUnitId']"
                                                                label="Saleable Area Unit">
                                                                <ng-select [virtualScroll]="true"
                                                                    formControlName="saleableAreaUnitId"
                                                                    ResizableDropdown placeholder="ex. sq. feet."
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAreaUnitLoading}"
                                                                    [readonly]="addLeadForm.controls['saleableArea']?.value ? false : true"
                                                                    bindValue="id" bindLabel="unit"
                                                                    [items]="areaSizeUnits"
                                                                    (change)="onUnitChange('saleableAreaUnitId')"></ng-select>
                                                            </form-errors-wrapper>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div
                                                            [ngClass]="addLeadForm.controls['propertyArea'].value ? 'field-label-req' : 'field-label'">
                                                            Property Area</div>
                                                        <div class="align-center">
                                                            <div class="w-50 mr-6">
                                                                <form-errors-wrapper
                                                                    [control]="addLeadForm.controls['propertyArea']"
                                                                    label="Property Area">
                                                                    <input type="number" min="0" placeholder="ex. 1906"
                                                                        formControlName="propertyArea"
                                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}">
                                                                </form-errors-wrapper>
                                                            </div>
                                                            <div class="w-50">
                                                                <form-errors-wrapper
                                                                    [control]="addLeadForm.controls['propertyAreaUnitId']"
                                                                    label="Property Area Unit">
                                                                    <ng-select [virtualScroll]="true"
                                                                        formControlName="propertyAreaUnitId"
                                                                        ResizableDropdown placeholder="ex. sq. feet."
                                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAreaUnitLoading}"
                                                                        [readonly]="addLeadForm.controls['propertyArea']?.value ? false : true"
                                                                        bindValue="id" bindLabel="unit"
                                                                        [items]="areaSizeUnits"
                                                                        (change)="onUnitChange('propertyAreaUnitId')"></ng-select>
                                                                </form-errors-wrapper>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div
                                                            [ngClass]="addLeadForm.controls['netArea'].value ? 'field-label-req' : 'field-label'">
                                                            Net Area</div>
                                                        <div class="align-center">
                                                            <div class="w-50 mr-6">
                                                                <form-errors-wrapper
                                                                    [control]="addLeadForm.controls['netArea']"
                                                                    label="Net Area">
                                                                    <input type="number" min="0" placeholder="ex. 1906"
                                                                        formControlName="netArea"
                                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}">
                                                                </form-errors-wrapper>
                                                            </div>
                                                            <div class="w-50">
                                                                <form-errors-wrapper
                                                                    [control]="addLeadForm.controls['netAreaUnitId']"
                                                                    label="Net Area Unit">
                                                                    <ng-select [virtualScroll]="true"
                                                                        formControlName="netAreaUnitId"
                                                                        ResizableDropdown placeholder="ex. sq. feet."
                                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAreaUnitLoading}"
                                                                        [readonly]="addLeadForm.controls['netArea']?.value ? false : true"
                                                                        bindValue="id" bindLabel="unit"
                                                                        [items]="areaSizeUnits"
                                                                        (change)="onUnitChange('netAreaUnitId')"></ng-select>
                                                                </form-errors-wrapper>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Unit Number/Name</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="unitName"
                                                                id="inpLeadunitNumber"
                                                                [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                                data-automate-id="inpLeadunitNumber"
                                                                placeholder="ex. A1B2C">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Cluster Name</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="clusterName"
                                                                id="inpLeadClusterName"
                                                                [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                                data-automate-id="inpLeadClusterName"
                                                                placeholder="Enter cluster name">
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">Purpose</div>
                                                    <div class="form-group">
                                                        <ng-select [virtualScroll]="true" [items]="purposeList"
                                                            ResizableDropdown
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                            placeholder="Select purpose" formControlName="purpose"
                                                            bindLabel="displayName" bindValue="value">
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEAD_FORM.enquired-for' | translate }}
                                                    </div>
                                                    <ng-select [virtualScroll]="true" [items]="enquiredForList"
                                                        [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                        class="ng-select-gray" bindLabel="type" bindValue="type"
                                                        formControlName="enquiredFor"
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                        placeholder="{{ 'GLOBAL.select' | translate }} {{'LEAD_FORM.enquired-for' | translate }}">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                            let-index="index">
                                                            <div class="checkbox-container"><input type="checkbox"
                                                                    id="item-{{index}}"
                                                                    data-automate-id="item-{{index}}"
                                                                    [checked]="item$.selected"><span
                                                                    class="checkmark"></span><span
                                                                    class="text-truncate-1 break-all">
                                                                    {{item.type}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LABEL.property' | translate }}
                                                        {{'LABEL.type' |
                                                        translate }}</div>
                                                    <form-errors-wrapper
                                                        [control]="addLeadForm.controls['propertyTypeId']"
                                                        label="{{'LABEL.property' | translate }} {{'LABEL.type' | translate }}">
                                                        <ng-select [virtualScroll]="true" ResizableDropdown
                                                            placeholder="select property type"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [items]="propertyTypeList" bindValue="displayName"
                                                            bindLabel="displayName" formControlName="propertyTypeId">
                                                        </ng-select>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div
                                                        [ngClass]="addLeadForm.controls['propertyTypeId'].value ? 'field-label-req' : 'field-label'">
                                                        {{'PROPERTY.sub-type' | translate}}</div>
                                                    <form-errors-wrapper [control]="addLeadForm.controls['propSubType']"
                                                        label="{{'PROPERTY.sub-type' | translate}}">
                                                        <ng-select [virtualScroll]="true" ResizableDropdown
                                                            placeholder="select property sub-type" class="bg-white"
                                                            [closeOnSelect]="false" [multiple]="true"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            [items]="propSubTypes" bindValue="displayName"
                                                            bindLabel="displayName" formControlName="propSubType"
                                                            [readonly]="addLeadForm.controls['propertyTypeId'].value ? false: true">
                                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                                let-index="index">
                                                                <div class="checkbox-container "><input type="checkbox"
                                                                        id="item-{{index}}"
                                                                        data-automate-id="item-{{index}}"
                                                                        [checked]="item$.selected"><span
                                                                        class="checkmark"></span><span
                                                                        class="text-truncate-1 break-all">{{item?.displayName}}</span>
                                                                </div>
                                                            </ng-template>
                                                        </ng-select>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                            <ng-container
                                                *ngIf="addLeadForm.get('propertyTypeId').value === 'Residential' && addLeadForm.controls['propSubType'].value != 'Plot' && addLeadForm.controls['propertyTypeId'].value && !globalSettingsDetails?.isCustomLeadFormEnabled">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">{{'PROPERTY.bhk' | translate }}</div>
                                                        <ng-select [virtualScroll]="true" [items]="bhkNoList"
                                                            [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                            class="ng-select-gray" formControlName="noOfBHK"
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                            placeholder="select BHK">
                                                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4"
                                                                    (click)="clear(item)"></span>
                                                                <span
                                                                    class="ng-value-label">{{getBHKDisplayString(item)}}</span>
                                                            </ng-template>
                                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                                let-index="index">
                                                                <div class="checkbox-container"><input type="checkbox"
                                                                        id="item-{{index}}"
                                                                        data-automate-id="item-{{index}}"
                                                                        [checked]="item$.selected"><span
                                                                        class="checkmark"></span><span
                                                                        class="text-truncate-1 break-all">
                                                                        {{getBHKDisplayString(item)}}</span>
                                                                </div>
                                                            </ng-template>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">{{'PROPERTY.bhk' | translate }}
                                                            {{'LABEL.type' |
                                                            translate}}</div>
                                                        <ng-select [virtualScroll]="true" [items]="bhkTypes"
                                                            [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                            class="ng-select-gray" formControlName="bhkType"
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                            placeholder="select BHK type">
                                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                                let-index="index">
                                                                <div class="checkbox-container"><input type="checkbox"
                                                                        id="item-{{index}}"
                                                                        data-automate-id="item-{{index}}"
                                                                        [checked]="item$.selected"><span
                                                                        class="checkmark"></span><span
                                                                        class="text-truncate-1 break-all">{{item}}</span>
                                                                </div>
                                                            </ng-template>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-container
                                                *ngIf="addLeadForm.controls['propertyTypeId'].value && globalSettingsDetails?.isCustomLeadFormEnabled">
                                                <ng-container
                                                    *ngIf="addLeadForm.get('propertyTypeId').value === 'Residential' && addLeadForm.controls['propSubType'].value != 'Plot'">
                                                    <!-- <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">BR</div>
                                                            <ng-select [virtualScroll]="true" [items]="numbers10"
                                                                [multiple]="true" [closeOnSelect]="false"
                                                                ResizableDropdown class="ng-select-gray"
                                                                bindLabel="display" bindValue="value"
                                                                formControlName="noOfBHK"
                                                                [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                placeholder="select BR">
                                                                <ng-template ng-option-tmp let-item="item"
                                                                    let-item$="item$" let-index="index">
                                                                    <div class="checkbox-container"><input
                                                                            type="checkbox" id="item-{{index}}"
                                                                            data-automate-id="item-{{index}}"
                                                                            [checked]="item$.selected"><span
                                                                            class="checkmark"></span><span
                                                                            class="text-truncate-1 break-all">
                                                                            {{item.display}}</span>
                                                                    </div>
                                                                </ng-template>
                                                            </ng-select>
                                                        </div>
                                                    </div> -->
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Baths</div>
                                                            <ng-select [virtualScroll]="true" [items]="numbers10"
                                                                [multiple]="true" [closeOnSelect]="false"
                                                                ResizableDropdown class="ng-select-gray"
                                                                bindLabel="display" bindValue="value"
                                                                formControlName="baths"
                                                                [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                placeholder="select baths">
                                                                <ng-template ng-option-tmp let-item="item"
                                                                    let-item$="item$" let-index="index">
                                                                    <div class="checkbox-container"><input
                                                                            type="checkbox" id="item-{{index}}"
                                                                            data-automate-id="item-{{index}}"
                                                                            [checked]="item$.selected"><span
                                                                            class="checkmark"></span><span
                                                                            class="text-truncate-1 break-all">
                                                                            {{item.display}}</span>
                                                                    </div>
                                                                </ng-template>
                                                            </ng-select>
                                                        </div>
                                                    </div>
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Beds</div>
                                                            <ng-select [virtualScroll]="true" [items]="numbers"
                                                                [multiple]="true" [closeOnSelect]="false"
                                                                ResizableDropdown class="ng-select-gray"
                                                                bindLabel="display" bindValue="value"
                                                                formControlName="beds"
                                                                [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                placeholder="select beds">
                                                                <ng-template ng-option-tmp let-item="item"
                                                                    let-item$="item$" let-index="index">
                                                                    <div class="checkbox-container"><input
                                                                            type="checkbox" id="item-{{index}}"
                                                                            data-automate-id="item-{{index}}"
                                                                            [checked]="item$.selected"><span
                                                                            class="checkmark"></span><span
                                                                            class="text-truncate-1 break-all">
                                                                            {{item.display}}</span>
                                                                    </div>
                                                                </ng-template>
                                                            </ng-select>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                <ng-container
                                                    *ngIf="addLeadForm.get('propertyTypeId')?.value !== 'Agricultural'">
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Preferred Floor(s)</div>
                                                            <ng-select [virtualScroll]="true" [items]="floorOptions"
                                                                [multiple]="true" [closeOnSelect]="false"
                                                                ResizableDropdown formControlName="preferredFloors"
                                                                placeholder="Select Preferred Floor"
                                                                class="ng-select-gray">
                                                                <ng-template ng-option-tmp let-item="item"
                                                                    let-item$="item$" let-index="index">
                                                                    <div class="checkbox-container"><input
                                                                            type="checkbox" id="item-{{index}}"
                                                                            data-automate-id="item-{{index}}"
                                                                            [checked]="item$.selected"><span
                                                                            class="checkmark"></span><span
                                                                            class="text-truncate-1 break-all">
                                                                            {{item}}</span>
                                                                    </div>
                                                                </ng-template>
                                                            </ng-select>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                <ng-container
                                                    *ngIf="addLeadForm.get('propertyTypeId')?.value !== 'Agricultural'">
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">
                                                                {{'PROPERTY.PROPERTY_DETAIL.furnish-status' |
                                                                translate }}</div>
                                                            <form-errors-wrapper
                                                                [control]="addLeadForm.controls['furnishStatus']"
                                                                label="{{'PROPERTY.PROPERTY_DETAIL.furnish-status' | translate }}">
                                                                <ng-select [virtualScroll]="true" ResizableDropdown
                                                                    placeholder="{{ 'GLOBAL.select' | translate }} Furnish Status"
                                                                    [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched ||  activeLeadIsLoading)}"
                                                                    [items]="furnishStatusList" bindValue="dispName"
                                                                    bindLabel="dispName"
                                                                    formControlName="furnishStatus">
                                                                </ng-select>
                                                            </form-errors-wrapper>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                            </ng-container>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{ 'SIDEBAR.project' | translate
                                                        }}(s)</div>
                                                    <ng-select [virtualScroll]="true" [items]="projectList"
                                                        [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || projectListIsLoading}"
                                                        [addTag]="true" bindLabel="project" bindValue="project"
                                                        name="projectsList" formControlName="projectsList"
                                                        addTagText="Create New Project" class="bg-white"
                                                        placeholder="{{ 'GLOBAL.select' | translate }}/Create {{ 'SIDEBAR.project' | translate }}">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                            let-index="index">
                                                            <div class="checkbox-container"><input type="checkbox"
                                                                    id="item-{{index}}"
                                                                    data-automate-id="item-{{index}}"
                                                                    [checked]="item$.selected"><span
                                                                    class="checkmark"></span><span
                                                                    class="text-truncate-1 break-all"> {{item.project
                                                                    ||
                                                                    item}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{ 'LABEL.property' |
                                                        translate}}(s)</div>
                                                    <ng-select [virtualScroll]="true" [items]="propertyList"
                                                        [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || propertyListIsLoading}"
                                                        [addTag]="true" bindLabel="property" bindValue="property"
                                                        name="propertiesList" formControlName="propertiesList"
                                                        addTagText="Create New Property"
                                                        placeholder="{{ 'GLOBAL.select' | translate }}/Create Property"
                                                        class="bg-white">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                            let-index="index">
                                                            <div class="checkbox-container"><input type="checkbox"
                                                                    id="item-{{index}}"
                                                                    data-automate-id="item-{{index}}"
                                                                    [checked]="item$.selected"><span
                                                                    class="checkmark"></span><span
                                                                    class="text-truncate-1 break-all">
                                                                    {{item.property ||
                                                                    item}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Offering Type</div>
                                                        <div class="form-group">
                                                            <ng-select [virtualScroll]="true" [items]="offerType"
                                                                ResizableDropdown
                                                                [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                placeholder="Select Offering Type"
                                                                formControlName="offeringType" bindLabel="displayName"
                                                                bindValue="value">
                                                            </ng-select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'INTEGRATION.agency-name' |
                                                        translate}}(s)</div>
                                                    <ng-select [virtualScroll]="true" [items]="agencyNameList"
                                                        [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAgencyNameListLoading}"
                                                        [addTag]="true" bindLabel="name" bindValue="name" name="name"
                                                        formControlName="agencies" addTagText="Create New Agency Name"
                                                        class="bg-white" placeholder="Select/Create Agency Name">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                            let-index="index">
                                                            <div class="checkbox-container"><input type="checkbox"
                                                                    id="item-{{index}}"
                                                                    data-automate-id="item-{{index}}"
                                                                    [checked]="item$.selected"><span
                                                                    class="checkmark"></span><span
                                                                    class="text-truncate-1 break-all">
                                                                    {{item}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEAD_FORM.campaign-name' |
                                                        translate}}(s)</div>
                                                    <ng-select [virtualScroll]="true" [items]="campaigns"
                                                        [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isCampaignListLoading}"
                                                        [addTag]="true" addTagText="Create New Campaign"
                                                        formControlName="campaigns"
                                                        placeholder="{{ 'GLOBAL.select' | translate }}/Create Campaign"
                                                        class="bg-white">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                            let-index="index">
                                                            <div class="checkbox-container"><input type="checkbox"
                                                                    id="item-{{index}}"
                                                                    data-automate-id="item-{{index}}"
                                                                    [checked]="item$.selected"><span
                                                                    class="checkmark"></span><span
                                                                    class="text-truncate-1 break-all">
                                                                    {{item}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEAD_FORM.channel-partner-name' |
                                                        translate}}(s)</div>
                                                    <ng-select [virtualScroll]="true" [items]="channelPartnerList"
                                                        [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isChannelPartnerListLoading}"
                                                        [addTag]="true" addTagText="Create New Channel Partner"
                                                        formControlName="channelPartnerList"
                                                        placeholder="{{ 'GLOBAL.select' | translate }}/Create Channel Partner"
                                                        class="bg-white">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                            let-index="index">
                                                            <div class="checkbox-container"><input type="checkbox"
                                                                    id="item-{{index}}"
                                                                    data-automate-id="item-{{index}}"
                                                                    [checked]="item$.selected"><span
                                                                    class="checkmark"></span><span
                                                                    class="text-truncate-1 break-all">{{item}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">Possession Needed By</div>
                                                    <app-possession-filter
                                                        [formControlNames]="{
                                                            possessionType: 'possessionType',
                                                            fromDate: 'fromPossessionDate',
                                                            toDate: 'toPossessionDate',
                                                            customDate: 'customPossessionDate'
                                                        }"
                                                        [initialPossessionType]="addLeadForm.get('possessionType')?.value"
                                                        [initialFromPossessionDate]="addLeadForm.get('fromPossessionDate')?.value"
                                                        [initialToPossessionDate]="addLeadForm.get('toPossessionDate')?.value"
                                                        [userTimeZoneOffset]="userBasicDetails?.timeZoneInfo?.baseUTcOffset"
                                                        (possessionFilterChange)="onPossessionFilterChange($event)">
                                                    </app-possession-filter>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="isShowManualLocation == false else manualLocation">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="justify-between align-end">
                                                            <div class="field-label text-nowrap mr-8">
                                                                {{'INTEGRATION.enquired-location'
                                                                |
                                                                translate}}</div>
                                                            <div class="cursor-pointer align-center fw-semi-bold text-accent-green text-sm mr-2 mb-4"
                                                                (click)="isShowManualLocation = !isShowManualLocation">
                                                                <span
                                                                    class="icon ic-xxxs ic-accent-green mr-4 ic-add"></span>
                                                                <span
                                                                    class="text-truncate-1 text-nowrap break-all">Manually
                                                                    Enter Location</span>
                                                            </div>
                                                        </div>
                                                        <ng-container
                                                            *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled; else googleLocation">
                                                            <div class="field-tag">
                                                                <ng-select formControlName="enquiredLocationId"
                                                                    [items]="internationalLocations"
                                                                    bindLabel="location" [multiple]="true"
                                                                    placeholder="Search Location"
                                                                    (search)="searchPlaceTerm$.next($event.term)"
                                                                    [searchFn]="customSearch"
                                                                    [editableSearchTerm]="true" [closeOnSelect]="false"
                                                                    [clearSearchOnAdd]="true" [virtualScroll]="true"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched ||  activeLeadIsLoading))}"
                                                                    class="bg-white"
                                                                    (close)="isBackSpace=true; searchPlaceTerm$.next('')"
                                                                    (clear)="removeLocation('location'); isBackSpace=true; searchPlaceTerm$.next('')"
                                                                    (change)="removeLocation('changeLocation')"
                                                                    (keydown)="onKeydown($event)" ResizableDropdown>
                                                                    <ng-template ng-label-tmp let-item="item"
                                                                        let-clear="clear">
                                                                        <span class="ng-value-label"> {{
                                                                            getFormattedLocation(item?.location)
                                                                            }}</span>
                                                                    </ng-template>
                                                                    <ng-template ng-option-tmp let-item="item"
                                                                        let-item$="item$" let-index="index">
                                                                        <div class="checkbox-container"><input
                                                                                type="checkbox" id="item-{{index}}"
                                                                                data-automate-id="item-{{index}}"
                                                                                [checked]="item$.selected"><span
                                                                                class="checkmark"></span>
                                                                            <div
                                                                                [title]="getFormattedLocation(item?.location)">
                                                                                {{ getFormattedLocation(item?.location)
                                                                                }}
                                                                            </div>
                                                                        </div>
                                                                    </ng-template>
                                                                </ng-select>
                                                                <div class="position-absolute top-5 right-40"
                                                                    *ngIf="isInternationalLocationLoading">
                                                                    <img src="assets/images/loader-rat.svg"
                                                                        class="rat-loader h-30px w-30px" alt="loader">
                                                                </div>
                                                                <div class="search icon ic-search ic-sm ic-coal"></div>
                                                            </div>
                                                        </ng-container>
                                                        <ng-template #googleLocation>
                                                            <div class="field-tag">
                                                                <ng-select formControlName="enquiredLocationId"
                                                                    [items]="placesList" bindLabel="location"
                                                                    [multiple]="true" placeholder="Search Location"
                                                                    (search)="searchPlaceTerm$.next($event.term)"
                                                                    [searchFn]="customSearch"
                                                                    [editableSearchTerm]="true" [closeOnSelect]="false"
                                                                    [clearSearchOnAdd]="true" [virtualScroll]="true"
                                                                    [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                                    class="bg-white"
                                                                    (close)="placesList=[]; isBackSpace=true; searchPlaceTerm$.next('')"
                                                                    (clear)="removeLocation('location'); isBackSpace=true; searchPlaceTerm$.next('')"
                                                                    (change)="removeLocation('changeLocation')"
                                                                    (keydown)="onKeydown($event)" ResizableDropdown>
                                                                    <ng-template ng-option-tmp let-item="item"
                                                                        let-item$="item$" let-index="index">
                                                                        <div class="checkbox-container"><input
                                                                                type="checkbox" id="item-{{index}}"
                                                                                data-automate-id="item-{{index}}"
                                                                                [checked]="item$.selected"><span
                                                                                class="checkmark"></span>
                                                                            <div [title]="item?.location">
                                                                                {{item?.location}}
                                                                            </div>
                                                                        </div>
                                                                    </ng-template>
                                                                </ng-select>
                                                                <div class="position-absolute top-5 right-40"
                                                                    *ngIf="isPlacesListLoading">
                                                                    <img src="assets/images/loader-rat.svg"
                                                                        class="rat-loader h-30px w-30px" alt="loader">
                                                                </div>
                                                                <div class="search icon ic-search ic-sm ic-coal"></div>
                                                            </div>
                                                        </ng-template>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-template #manualLocation>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="justify-between align-end">
                                                            <div class="field-label">Enquired Locality</div>
                                                            <div class="cursor-pointer align-center fw-semi-bold text-accent-green text-sm mr-2 mb-4"
                                                                (click)="isShowManualLocation = !isShowManualLocation">
                                                                <span
                                                                    class="icon ic-xxxs ic-accent-green mr-4 ic-search"></span>
                                                                <span
                                                                    class="text-truncate-1 text-nowrap break-all">Location
                                                                    List</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="enquiredLocality"
                                                                placeholder="enter locality"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                </div>
                                                <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Enquired Sub-Community</div>
                                                            <div class="form-group">
                                                                <input type="text"
                                                                    formControlName="enquiredSubCommunity"
                                                                    placeholder="enter sub-community">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Enquired Community</div>
                                                            <div class="form-group">
                                                                <input type="text" formControlName="enquiredCommunity"
                                                                    placeholder="enter community">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                        <div class="mr-12">
                                                            <div class="field-label">Enquired Tower Name</div>
                                                            <div class="form-group">
                                                                <input type="text" formControlName="enquiredTowerName"
                                                                    placeholder="enter tower name">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-container>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">{{'INTEGRATION.enquired-city' |
                                                            translate}}</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="enquiredCity"
                                                                placeholder="enter locality"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">{{'INTEGRATION.enquired-state' |
                                                            translate}}</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="enquiredState"
                                                                placeholder="enter state"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Enquired Country</div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="enquiredCountry"
                                                                placeholder="enter country"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="justify-between align-end">
                                                            <div class="field-label">Enquired Pincode</div>
                                                            <div class="cursor-pointer align-center fw-semi-bold text-accent-green text-sm mr-2 mb-4"
                                                                *ngIf="isShowManualLocation"
                                                                (click)="addMoreLocation()">
                                                                <span>Add another location</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="text" formControlName="enquiredPincode"
                                                                placeholder="enter pincode"
                                                                (change)="removeLocation('changeLocality')">
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </div>
                                        <div class="d-flex flex-wrap px-20">
                                            <ng-container *ngFor="let location of manualLocationsList">
                                                <div *ngIf="getLocationDetailsByObj(location)"
                                                    class="bg-light-pearl px-8 py-6 br-20 mr-10 mb-12 align-center text-wrap word-break">
                                                    {{ getLocationDetailsByObj(location)}}
                                                    <span class="ic-close ic-red-750 icon ic-xxs ml-8 cursor-pointer"
                                                        (click)="clearManualLocation(location)"></span>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'Additional Info'">
                                        <div class="d-flex flex-wrap pl-20 pb-20 pr-8">
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEADS.profession' |
                                                        translate}}</div>
                                                    <div class="form-group">
                                                        <ng-select [virtualScroll]="true" [items]="profession"
                                                            [closeOnSelect]="true" ResizableDropdown
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                            placeholder="Select Profession"
                                                            formControlName="profession">
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'AUTH.company-name' |
                                                        translate}}</div>
                                                    <div class="form-group">
                                                        <input type="text" formControlName="companyName"
                                                            id="inpLeadCompanyName"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}"
                                                            data-automate-id="inpLeadCompanyName"
                                                            placeholder="ex. ABC Company">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'USER_MANAGEMENT.designation' |
                                                        translate}}</div>
                                                    <div class="form-group">
                                                        <input type="text" formControlName="designation"
                                                            placeholder="ex. Software Developer"
                                                            [ngClass]="{'blinking pe-none': !isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)}">
                                                    </div>
                                                </div>
                                            </div>
                                            <ng-container *ngIf="globalSettingsDetails?.isCustomLeadFormEnabled">
                                                <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                    <div class="mr-12">
                                                        <div class="field-label">Nationality
                                                        </div>
                                                        <ng-select formControlName="nationality"
                                                            placeholder="Search nationality" [closeOnSelect]="true"
                                                            [virtualScroll]="true"
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                            class="bg-white" ResizableDropdown>
                                                            <ng-option *ngFor="let item of nationalities"
                                                                [value]="item?.name">
                                                                {{ item?.name }}
                                                            </ng-option>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">Gender
                                                    <div class="form-group">
                                                        <ng-select [virtualScroll]="true"
                                                            formControlName="gender" ResizableDropdown [items]="gender" bindValue="id" bindLabel="displayName"
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                            placeholder="Select Gender">
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                            <div class="mr-12">
                                                <div class="field-label">Date of Birth</div>
                                                <div class="form-group">
                                                    <input type="text" formControlName="dateOfBirth" placeholder="23/04/2002" [owlDateTimeTrigger]="dt2"
                                                    [owlDateTime]="dt2"  [max]="maxDate" />
                                                <owl-date-time #dt2 (afterPickerOpen)="onPickerOpened(currentDate)"
                                                    [pickerType]="'calendar'"></owl-date-time>                                                  
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                            <div class="mr-12">
                                                <div class="field-label">Marital Status</div>
                                                <div class="form-group">
                                                    <ng-select [virtualScroll]="true" [items]="maritalStatus"
                                                        formControlName="maritalStatus" ResizableDropdown
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                        placeholder="Select Marital Status" bindValue="value" bindLabel="name">
                                                    </ng-select>
                                                </div>
                                            </div>
                                        </div>
                                        </div>

                                    </ng-container>
                                    <ng-container *ngSwitchCase="'Others'">
                                        <div class="d-flex flex-wrap pl-20 pb-20 pr-8">
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEADS.sourcing-manager' |
                                                        translate}}</div>
                                                    <div class="form-group">
                                                        <ng-select [virtualScroll]="true"
                                                            formControlName="sourcingManager" ResizableDropdown
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAllUsersLoading}"
                                                            placeholder="Select Sourcing Manager">
                                                            <ng-option *ngFor="let user of activeUsers"
                                                                [value]="user.id">
                                                                {{user.firstName}} {{user.lastName}} <span
                                                                    class="d-none">{{user.fullName}}</span></ng-option>
                                                            <ng-option *ngFor="let user of inactiveUsers"
                                                                [value]="user.id" [disabled]="true">
                                                                <div class="dropdown-position"> <span
                                                                        class="text-truncate-1 break-all"> {{
                                                                        user.firstName
                                                                        }} {{
                                                                        user.lastName }}</span> <span
                                                                        class="d-none">{{user.fullName}}</span><span
                                                                        class="text-disabled" *ngIf="!user.isActive">
                                                                        (Disabled)</span></div>
                                                            </ng-option>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tv-w-20 w-25 tb-w-33 ip-w-50 ph-w-100">
                                                <div class="mr-12">
                                                    <div class="field-label">{{'LEADS.closing-manager' |
                                                        translate}}</div>
                                                    <div class="form-group">
                                                        <ng-select [virtualScroll]="true"
                                                            formControlName="closingManager" ResizableDropdown
                                                            [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading)) || isAllUsersLoading}"
                                                            placeholder="Select Closing Manager">
                                                            <ng-option *ngFor="let user of activeUsers"
                                                                [value]="user.id">
                                                                {{user.firstName}} {{user.lastName}} <span
                                                                    class="d-none">{{user.fullName}}</span></ng-option>
                                                            <ng-option *ngFor="let user of inactiveUsers"
                                                                [value]="user.id" [disabled]="true">
                                                                <div class="dropdown-position"> <span
                                                                        class="text-truncate-1 break-all"> {{
                                                                        user.firstName
                                                                        }} {{
                                                                        user.lastName }}</span> <span
                                                                        class="d-none">{{user.fullName}}</span><span
                                                                        class="text-disabled" *ngIf="!user.isActive">
                                                                        (Disabled)</span></div>
                                                            </ng-option>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'Notes'">
                                        <div class="d-flex flex-wrap pl-20 pb-20 pr-8">
                                            <div class="w-100 mr-12">
                                                <div class="flex-between">
                                                    <div
                                                        [ngClass]="isNotesMandatory ? 'field-label-req' : 'field-label'">
                                                        {{'TASK.notes'|
                                                        translate}}</div>
                                                    <div *ngIf="addLeadForm.controls['notes'].value"
                                                        class="cursor-pointer mt-16" (click)="openPreview()">
                                                        <h6 class="flex-center text-green-250 fw-400"><span
                                                                class="ic-eye-solid ic-xxs ic-accent-green mr-4"></span>Preview
                                                        </h6>
                                                    </div>
                                                </div>
                                                <form-errors-wrapper>
                                                    <textarea rows="3" id="txtLeadNotes" data-automate-id="txtLeadNotes"
                                                        placeholder="ex. I want to say .... "
                                                        [ngClass]="{'blinking pe-none': (!isAddLead && (!isSelectedLeadInfoFetched || activeLeadIsLoading))}"
                                                        formControlName="notes"></textarea>
                                                </form-errors-wrapper>
                                            </div>
                                        </div>
                                    </ng-container>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
        </form>
    </div>
    <div class="flex-end px-20 py-16 bg-white">
        <button class="btn-gray" (click)="goToManageLead()">{{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal ml-20" (click)="isduplicateLead ? confirmDuplicate() : postData()"
            [ngClass]="{'pe-none opacity-5': !addLeadForm?.dirty || isPostingData}"><span
                *ngIf="!isPostingData else buttonDots">
                {{'BUTTONS.save' | translate }}
            </span></button>
    </div>
</ng-container>
<ng-template #previewModal>
    <div class="w-100 br-10">
        <div class="p-20">
            <div class="flex-between">
                <h5 class="fw-600">Notes
                </h5>
                <div class="cursor-pointer flex-center header-5 fw-400 text-red-350" (click)="modalRef.hide()"><span
                        class="ic-close mb-2 ic-xxs ic-red-350 mr-4"></span>Close
                </div>
            </div>
            <div class="scrollbar max-h-400 border br-6 mt-8 p-16">
                <h6 [innerHTML]="convertUrlsToLinks(addLeadForm.controls['notes'].value,true)"
                    class="text-sm text-black-20 pre-whitespace"></h6>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>