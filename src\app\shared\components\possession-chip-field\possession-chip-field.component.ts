import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Subject } from 'rxjs';
import { POSSESSION_DATE_FILTER_LIST } from 'src/app/app.constants';
import { PossessionType } from 'src/app/app.enum';

@Component({
  selector: 'app-possession-chip-field',
  templateUrl: './possession-chip-field.component.html',
  styleUrls: ['./possession-chip-field.component.scss']
})
export class PossessionChipFieldComponent implements OnInit, OnDestroy, OnChanges {
  @Input() initialPossessionType: number | null = null;
  @Input() initialCustomDate: string | null = null;
  @Input() label: string = 'Possession Needed By';
  @Input() showLabel: boolean = true;
  @Input() radioGroupName: string = 'possessionGroup';
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  
  @Output() possessionChange = new EventEmitter<{
    possessionType: number | null;
    customDate: string | null;
    displayValue: string;
  }>();
  
  @ViewChild('dtCustomPossession') dtCustomPossession: OwlDateTimeComponent<any>;

  possessionOptions = POSSESSION_DATE_FILTER_LIST;
  selectedPossession: string | null = null;
  customDate: Date | null = null;
  minDate = new Date(); // Today's date as minimum
  showDateValidationError = false;
  
  private stopper = new Subject<void>();

  constructor() { }

  ngOnInit(): void {
    this.initializeValues();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialPossessionType'] || changes['initialCustomDate']) {
      this.initializeValues();
    }
  }

  initializeValues(): void {
    if (this.initialPossessionType) {
      const possessionType = PossessionType[this.initialPossessionType] as string;
      if (possessionType) {
        this.selectedPossession = possessionType;
        
        if (possessionType === 'Custom Date' && this.initialCustomDate) {
          this.customDate = new Date(this.initialCustomDate);
        }
      }
    }
  }

  selectPossession(value: string): void {
    if (this.disabled) return;
    
    this.selectedPossession = value;
    this.showDateValidationError = false;
    
    if (value !== 'Custom Date') {
      this.customDate = null;
      this.emitChange();
    }
  }

  onCustomDateChange(event: any): void {
    if (event) {
      this.customDate = new Date(event);
      this.validateCustomDate();
    }
  }

  validateCustomDate(): boolean {
    if (this.selectedPossession === 'Custom Date') {
      if (!this.customDate) {
        this.showDateValidationError = true;
        return false;
      }
      
      // Check if date is in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.customDate);
      selectedDate.setHours(0, 0, 0, 0);
      
      if (selectedDate < today) {
        this.showDateValidationError = true;
        return false;
      }
      
      this.showDateValidationError = false;
      this.emitChange();
      return true;
    }
    return true;
  }

  getFormattedCustomDate(): string {
    if (!this.customDate) return '';
    
    const date = new Date(this.customDate);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}-${month}-${year}`;
  }

  getDisplayValue(): string {
    if (!this.selectedPossession) return '';
    
    const selectedOption = this.possessionOptions.find(option => option.value === this.selectedPossession);
    if (selectedOption) {
      if (this.selectedPossession === 'Custom Date' && this.customDate) {
        return `Custom (${this.getFormattedCustomDate()})`;
      }
      return selectedOption.displayName;
    }
    return this.selectedPossession;
  }

  emitChange(): void {
    const possessionTypeValue = this.selectedPossession ? 
      PossessionType[this.selectedPossession as keyof typeof PossessionType] : null;
    
    this.possessionChange.emit({
      possessionType: possessionTypeValue || null,
      customDate: this.customDate ? this.customDate.toISOString() : null,
      displayValue: this.getDisplayValue()
    });
  }

  reset(): void {
    this.selectedPossession = null;
    this.customDate = null;
    this.showDateValidationError = false;
    this.emitChange();
  }

  isValid(): boolean {
    if (!this.required) return true;
    if (!this.selectedPossession) return false;
    if (this.selectedPossession === 'Custom Date') {
      return this.validateCustomDate();
    }
    return true;
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
